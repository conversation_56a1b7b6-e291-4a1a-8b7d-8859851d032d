package generators

// GenerateSnakeGameInstructions returns instructions for the Snake game
func GenerateSnakeGameInstructions() string {
	return "🐍 **Python Snake Game Instructions**\n\n" +
		"**How to run:**\n" +
		"1. Install pygame: `pip install pygame`\n" +
		"2. Save the code as `snake_game.py`\n" +
		"3. Run: `python snake_game.py`\n\n" +
		"**Controls:**\n" +
		"- Arrow keys to move the snake\n" +
		"- Eat red food to grow\n" +
		"- Don't hit walls or yourself!\n\n" +
		"**Features:**\n" +
		"- Smooth snake movement\n" +
		"- Collision detection\n" +
		"- Score tracking\n" +
		"- Game over handling"
}

// GenerateCalculatorInstructions returns instructions for the Calculator
func GenerateCalculatorInstructions() string {
	return "🧮 **Python Calculator Instructions**\n\n" +
		"**Features:**\n" +
		"- Basic arithmetic operations (+, -, *, /)\n" +
		"- Power operations (**)\n" +
		"- Modulo operations (%)\n" +
		"- Error handling for invalid expressions\n" +
		"- Interactive command-line interface\n\n" +
		"**How to run:**\n" +
		"1. Save the code as `calculator.py`\n" +
		"2. Run: `python calculator.py`\n" +
		"3. Enter mathematical expressions when prompted\n" +
		"4. Type 'quit' to exit\n\n" +
		"**Example usage:**\n" +
		"- `2 + 3` → 5\n" +
		"- `10 ** 2` → 100\n" +
		"- `15 % 4` → 3"
}

// GenerateTodoAppInstructions returns instructions for the Todo App
func GenerateTodoAppInstructions() string {
	return "📝 **Python Todo App Instructions**\n\n" +
		"**Features:**\n" +
		"- Add new todo items\n" +
		"- List all todos with status\n" +
		"- Mark todos as completed\n" +
		"- Delete individual todos\n" +
		"- Auto-save to JSON file\n" +
		"- Simple command-line interface\n\n" +
		"**How to run:**\n" +
		"1. Save the code as `todo_app.py`\n" +
		"2. Run: `python todo_app.py`\n" +
		"3. Follow the menu prompts\n\n" +
		"**Menu options:**\n" +
		"- `1` → Add new todo\n" +
		"- `2` → List all todos\n" +
		"- `3` → Complete a todo\n" +
		"- `4` → Delete a todo\n" +
		"- `5` → Quit application\n\n" +
		"**Data storage:**\n" +
		"- Todos are saved in `todos.json`\n" +
		"- Data persists between sessions"
}

// GenerateWebScraperInstructions returns instructions for the Web Scraper
func GenerateWebScraperInstructions() string {
	return "🌐 **Python Web Scraper Instructions**\n\n" +
		"**Required packages:**\n" +
		"```bash\n" +
		"pip install requests beautifulsoup4\n" +
		"```\n\n" +
		"**Features:**\n" +
		"- Scrape quotes from quotes.toscrape.com\n" +
		"- Extract news headlines from websites\n" +
		"- Error handling for network issues\n" +
		"- JSON output for data storage\n" +
		"- User-Agent headers for better compatibility\n\n" +
		"**How to run:**\n" +
		"1. Install required packages: `pip install requests beautifulsoup4`\n" +
		"2. Save the code as `web_scraper.py`\n" +
		"3. Run: `python web_scraper.py`\n\n" +
		"**What it does:**\n" +
		"- Scrapes inspirational quotes with authors and tags\n" +
		"- Extracts news headlines from Hacker News\n" +
		"- Saves quotes to `quotes.json` file\n" +
		"- Displays results in the terminal\n\n" +
		"**Note:** Always respect robots.txt and website terms of service!"
}
