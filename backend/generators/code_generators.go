package generators

// generateSnakeGameCode returns Python code for a Snake game
func GenerateSnakeGameCode() string {
	return "```python\n" +
		"import pygame\n" +
		"import random\n" +
		"import sys\n\n" +
		"# Initialize Pygame\n" +
		"pygame.init()\n\n" +
		"# Constants\n" +
		"WINDOW_WIDTH = 800\n" +
		"WINDOW_HEIGHT = 600\n" +
		"CELL_SIZE = 20\n" +
		"CELL_NUMBER_X = WINDOW_WIDTH // CELL_SIZE\n" +
		"CELL_NUMBER_Y = WINDOW_HEIGHT // CELL_SIZE\n\n" +
		"# Colors\n" +
		"BLACK = (0, 0, 0)\n" +
		"GREEN = (0, 255, 0)\n" +
		"RED = (255, 0, 0)\n" +
		"WHITE = (255, 255, 255)\n\n" +
		"class Snake:\n" +
		"    def __init__(self):\n" +
		"        self.body = [pygame.Vector2(5, 10), pygame.Vector2(4, 10), pygame.Vector2(3, 10)]\n" +
		"        self.direction = pygame.Vector2(1, 0)\n" +
		"        self.new_block = False\n\n" +
		"    def draw_snake(self, screen):\n" +
		"        for block in self.body:\n" +
		"            x_pos = int(block.x * CELL_SIZE)\n" +
		"            y_pos = int(block.y * CELL_SIZE)\n" +
		"            block_rect = pygame.Rect(x_pos, y_pos, CELL_SIZE, CELL_SIZE)\n" +
		"            pygame.draw.rect(screen, GREEN, block_rect)\n\n" +
		"    def move_snake(self):\n" +
		"        if self.new_block:\n" +
		"            body_copy = self.body[:]\n" +
		"            body_copy.insert(0, body_copy[0] + self.direction)\n" +
		"            self.body = body_copy[:]\n" +
		"            self.new_block = False\n" +
		"        else:\n" +
		"            body_copy = self.body[:-1]\n" +
		"            body_copy.insert(0, body_copy[0] + self.direction)\n" +
		"            self.body = body_copy[:]\n\n" +
		"    def add_block(self):\n" +
		"        self.new_block = True\n\n" +
		"    def check_collision(self):\n" +
		"        if not 0 <= self.body[0].x < CELL_NUMBER_X or not 0 <= self.body[0].y < CELL_NUMBER_Y:\n" +
		"            return True\n" +
		"        for block in self.body[1:]:\n" +
		"            if block == self.body[0]:\n" +
		"                return True\n" +
		"        return False\n\n" +
		"class Food:\n" +
		"    def __init__(self):\n" +
		"        self.randomize()\n\n" +
		"    def draw_food(self, screen):\n" +
		"        food_rect = pygame.Rect(int(self.pos.x * CELL_SIZE), int(self.pos.y * CELL_SIZE), CELL_SIZE, CELL_SIZE)\n" +
		"        pygame.draw.rect(screen, RED, food_rect)\n\n" +
		"    def randomize(self):\n" +
		"        self.x = random.randint(0, CELL_NUMBER_X - 1)\n" +
		"        self.y = random.randint(0, CELL_NUMBER_Y - 1)\n" +
		"        self.pos = pygame.Vector2(self.x, self.y)\n\n" +
		"class Game:\n" +
		"    def __init__(self):\n" +
		"        self.snake = Snake()\n" +
		"        self.food = Food()\n\n" +
		"    def update(self):\n" +
		"        self.snake.move_snake()\n" +
		"        self.check_collision()\n" +
		"        self.check_fail()\n\n" +
		"    def draw_elements(self, screen):\n" +
		"        screen.fill(BLACK)\n" +
		"        self.food.draw_food(screen)\n" +
		"        self.snake.draw_snake(screen)\n\n" +
		"    def check_collision(self):\n" +
		"        if self.food.pos == self.snake.body[0]:\n" +
		"            self.food.randomize()\n" +
		"            self.snake.add_block()\n\n" +
		"    def check_fail(self):\n" +
		"        if self.snake.check_collision():\n" +
		"            self.game_over()\n\n" +
		"    def game_over(self):\n" +
		"        pygame.quit()\n" +
		"        sys.exit()\n\n" +
		"def main():\n" +
		"    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))\n" +
		"    pygame.display.set_caption('Snake Game')\n" +
		"    clock = pygame.time.Clock()\n" +
		"    game = Game()\n\n" +
		"    SCREEN_UPDATE = pygame.USEREVENT\n" +
		"    pygame.time.set_timer(SCREEN_UPDATE, 150)\n\n" +
		"    while True:\n" +
		"        for event in pygame.event.get():\n" +
		"            if event.type == pygame.QUIT:\n" +
		"                pygame.quit()\n" +
		"                sys.exit()\n" +
		"            if event.type == SCREEN_UPDATE:\n" +
		"                game.update()\n" +
		"            if event.type == pygame.KEYDOWN:\n" +
		"                if event.key == pygame.K_UP:\n" +
		"                    if game.snake.direction.y != 1:\n" +
		"                        game.snake.direction = pygame.Vector2(0, -1)\n" +
		"                if event.key == pygame.K_DOWN:\n" +
		"                    if game.snake.direction.y != -1:\n" +
		"                        game.snake.direction = pygame.Vector2(0, 1)\n" +
		"                if event.key == pygame.K_RIGHT:\n" +
		"                    if game.snake.direction.x != -1:\n" +
		"                        game.snake.direction = pygame.Vector2(1, 0)\n" +
		"                if event.key == pygame.K_LEFT:\n" +
		"                    if game.snake.direction.x != 1:\n" +
		"                        game.snake.direction = pygame.Vector2(-1, 0)\n\n" +
		"        game.draw_elements(screen)\n" +
		"        pygame.display.update()\n" +
		"        clock.tick(60)\n\n" +
		"if __name__ == '__main__':\n" +
		"    main()\n" +
		"```"
}

// GenerateCalculatorCode returns Python code for a calculator
func GenerateCalculatorCode() string {
	return "```python\n" +
		"def calculator():\n" +
		"    print('Simple Calculator')\n" +
		"    print('Operations: +, -, *, /, ** (power), % (modulo)')\n" +
		"    print('Type \"quit\" to exit')\n" +
		"    \n" +
		"    while True:\n" +
		"        try:\n" +
		"            expression = input('Enter calculation (or \"quit\" to exit): ').strip()\n" +
		"            \n" +
		"            if expression.lower() == 'quit':\n" +
		"                print('Goodbye!')\n" +
		"                break\n" +
		"            \n" +
		"            if not expression:\n" +
		"                print('Please enter a valid expression')\n" +
		"                continue\n" +
		"            \n" +
		"            # Basic security check\n" +
		"            allowed_chars = '0123456789+-*/%(). '\n" +
		"            if not all(c in allowed_chars for c in expression):\n" +
		"                print('Error: Only numbers and basic operators are allowed')\n" +
		"                continue\n" +
		"            \n" +
		"            result = eval(expression)\n" +
		"            print(f'Result: {result}')\n" +
		"            \n" +
		"        except ZeroDivisionError:\n" +
		"            print('Error: Division by zero')\n" +
		"        except Exception as e:\n" +
		"            print(f'Error: Invalid expression - {e}')\n" +
		"\n" +
		"if __name__ == '__main__':\n" +
		"    calculator()\n" +
		"```"
}

// GenerateTodoAppCode returns Python code for a Todo app
func GenerateTodoAppCode() string {
	return "```python\n" +
		"import json\n" +
		"import os\n\n" +
		"class TodoApp:\n" +
		"    def __init__(self):\n" +
		"        self.todos = []\n" +
		"        self.load_todos()\n\n" +
		"    def show_menu(self):\n" +
		"        print('\\n📝 Todo App')\n" +
		"        print('1. Add todo')\n" +
		"        print('2. List todos')\n" +
		"        print('3. Complete todo')\n" +
		"        print('4. Delete todo')\n" +
		"        print('5. Quit')\n\n" +
		"    def add_todo(self):\n" +
		"        task = input('Enter new todo: ').strip()\n" +
		"        if task:\n" +
		"            self.todos.append({'task': task, 'completed': False})\n" +
		"            print(f'Added: {task}')\n" +
		"            self.save_todos()\n\n" +
		"    def list_todos(self):\n" +
		"        if not self.todos:\n" +
		"            print('No todos found!')\n" +
		"            return\n" +
		"        \n" +
		"        print('\\nYour todos:')\n" +
		"        for i, todo in enumerate(self.todos, 1):\n" +
		"            status = '✓' if todo['completed'] else '○'\n" +
		"            print(f'{i}. {status} {todo[\"task\"]}')\n\n" +
		"    def complete_todo(self):\n" +
		"        self.list_todos()\n" +
		"        try:\n" +
		"            index = int(input('Enter todo number to complete: ')) - 1\n" +
		"            if 0 <= index < len(self.todos):\n" +
		"                self.todos[index]['completed'] = True\n" +
		"                print(f'Completed: {self.todos[index][\"task\"]}')\n" +
		"                self.save_todos()\n" +
		"            else:\n" +
		"                print('Invalid todo number!')\n" +
		"        except ValueError:\n" +
		"            print('Please enter a valid number!')\n\n" +
		"    def delete_todo(self):\n" +
		"        self.list_todos()\n" +
		"        try:\n" +
		"            index = int(input('Enter todo number to delete: ')) - 1\n" +
		"            if 0 <= index < len(self.todos):\n" +
		"                deleted = self.todos.pop(index)\n" +
		"                print(f'Deleted: {deleted[\"task\"]}')\n" +
		"                self.save_todos()\n" +
		"            else:\n" +
		"                print('Invalid todo number!')\n" +
		"        except ValueError:\n" +
		"            print('Please enter a valid number!')\n\n" +
		"    def save_todos(self):\n" +
		"        with open('todos.json', 'w') as f:\n" +
		"            json.dump(self.todos, f, indent=2)\n\n" +
		"    def load_todos(self):\n" +
		"        if os.path.exists('todos.json'):\n" +
		"            with open('todos.json', 'r') as f:\n" +
		"                self.todos = json.load(f)\n\n" +
		"    def run(self):\n" +
		"        while True:\n" +
		"            self.show_menu()\n" +
		"            choice = input('Choose option (1-5): ').strip()\n" +
		"            \n" +
		"            if choice == '1':\n" +
		"                self.add_todo()\n" +
		"            elif choice == '2':\n" +
		"                self.list_todos()\n" +
		"            elif choice == '3':\n" +
		"                self.complete_todo()\n" +
		"            elif choice == '4':\n" +
		"                self.delete_todo()\n" +
		"            elif choice == '5':\n" +
		"                print('Goodbye!')\n" +
		"                break\n" +
		"            else:\n" +
		"                print('Invalid choice!')\n\n" +
		"if __name__ == '__main__':\n" +
		"    app = TodoApp()\n" +
		"    app.run()\n" +
		"```"
}

// GenerateWebScraperCode returns Python code for a web scraper
func GenerateWebScraperCode() string {
	return "```python\n" +
		"import requests\n" +
		"from bs4 import BeautifulSoup\n" +
		"import json\n\n" +
		"def scrape_quotes():\n" +
		"    \"\"\"Simple web scraper for quotes\"\"\"\n" +
		"    url = 'http://quotes.toscrape.com/'\n" +
		"    \n" +
		"    try:\n" +
		"        response = requests.get(url)\n" +
		"        response.raise_for_status()\n" +
		"        \n" +
		"        soup = BeautifulSoup(response.content, 'html.parser')\n" +
		"        quotes = []\n" +
		"        \n" +
		"        for quote in soup.find_all('div', class_='quote'):\n" +
		"            text = quote.find('span', class_='text').get_text()\n" +
		"            author = quote.find('small', class_='author').get_text()\n" +
		"            tags = [tag.get_text() for tag in quote.find_all('a', class_='tag')]\n" +
		"            \n" +
		"            quotes.append({\n" +
		"                'text': text,\n" +
		"                'author': author,\n" +
		"                'tags': tags\n" +
		"            })\n" +
		"        \n" +
		"        return quotes\n" +
		"        \n" +
		"    except requests.RequestException as e:\n" +
		"        print(f'Error: {e}')\n" +
		"        return []\n\n" +
		"def main():\n" +
		"    print('🕷️ Web Scraper Demo')\n" +
		"    \n" +
		"    # Scrape quotes\n" +
		"    print('\\n1. Scraping quotes...')\n" +
		"    quotes = scrape_quotes()\n" +
		"    if quotes:\n" +
		"        print(f'Found {len(quotes)} quotes')\n" +
		"        for i, quote in enumerate(quotes[:3], 1):\n" +
		"            print(f'{i}. \"{quote[\"text\"]}\" - {quote[\"author\"]}')\n" +
		"        \n" +
		"        # Save to JSON\n" +
		"        with open('quotes.json', 'w') as f:\n" +
		"            json.dump(quotes, f, indent=2)\n" +
		"        print('Saved to quotes.json')\n\n" +
		"if __name__ == '__main__':\n" +
		"    main()\n" +
		"```"
}
