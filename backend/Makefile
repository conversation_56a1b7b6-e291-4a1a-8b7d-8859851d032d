# Makefile for Football AI Go Backend

# Variables
BINARY_NAME=football-ai-backend
MAIN_PATH=.
BUILD_DIR=build
GO_FILES=$(shell find . -name "*.go" -type f)

# Default target
.PHONY: all
all: build

# Build the application
.PHONY: build
build:
	@echo "🔨 Building $(BINARY_NAME)..."
	@go build -o $(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ Build completed: $(BINARY_NAME)"

# Build for production (with optimizations)
.PHONY: build-prod
build-prod:
	@echo "🔨 Building $(BINARY_NAME) for production..."
	@CGO_ENABLED=0 go build -ldflags="-w -s" -o $(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ Production build completed: $(BINARY_NAME)"

# Run the application
.PHONY: run
run:
	@echo "🚀 Running $(BINARY_NAME)..."
	@go run $(MAIN_PATH)

# Run with hot reload (requires air)
.PHONY: dev
dev:
	@echo "🔥 Starting development server with hot reload..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "❌ Air not found. Install with: go install github.com/cosmtrek/air@latest"; \
		echo "🔄 Falling back to normal run..."; \
		go run $(MAIN_PATH); \
	fi

# Test the application
.PHONY: test
test:
	@echo "🧪 Running tests..."
	@go test -v ./...

# Test with coverage
.PHONY: test-coverage
test-coverage:
	@echo "🧪 Running tests with coverage..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "📊 Coverage report generated: coverage.html"

# Format code
.PHONY: fmt
fmt:
	@echo "🎨 Formatting code..."
	@go fmt ./...

# Lint code
.PHONY: lint
lint:
	@echo "🔍 Linting code..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "❌ golangci-lint not found. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# Tidy dependencies
.PHONY: tidy
tidy:
	@echo "📦 Tidying dependencies..."
	@go mod tidy

# Download dependencies
.PHONY: deps
deps:
	@echo "📥 Downloading dependencies..."
	@go mod download

# Clean build artifacts
.PHONY: clean
clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -f $(BINARY_NAME)
	@rm -f coverage.out coverage.html
	@rm -rf $(BUILD_DIR)
	@echo "✅ Clean completed"

# Install development tools
.PHONY: install-tools
install-tools:
	@echo "🛠️ Installing development tools..."
	@go install github.com/cosmtrek/air@latest
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "✅ Development tools installed"

# Check if Go is installed
.PHONY: check-go
check-go:
	@echo "🔍 Checking Go installation..."
	@if command -v go > /dev/null; then \
		echo "✅ Go is installed: $$(go version)"; \
	else \
		echo "❌ Go is not installed. Please install Go 1.21 or later."; \
		echo "📖 Visit: https://golang.org/dl/"; \
		exit 1; \
	fi

# Setup development environment
.PHONY: setup
setup: check-go deps install-tools
	@echo "🎉 Development environment setup completed!"

# Run backend tests
.PHONY: test-backend
test-backend: build
	@echo "🧪 Testing backend endpoints..."
	@if [ -f "./test_backend.sh" ]; then \
		chmod +x ./test_backend.sh; \
		./test_backend.sh; \
	else \
		echo "❌ test_backend.sh not found"; \
	fi

# Docker build (if needed)
.PHONY: docker-build
docker-build:
	@echo "🐳 Building Docker image..."
	@docker build -t football-ai-backend .

# Show help
.PHONY: help
help:
	@echo "🏈 Football AI Go Backend - Available Commands:"
	@echo ""
	@echo "  build          Build the application"
	@echo "  build-prod     Build for production (optimized)"
	@echo "  run            Run the application"
	@echo "  dev            Run with hot reload (requires air)"
	@echo "  test           Run tests"
	@echo "  test-coverage  Run tests with coverage report"
	@echo "  fmt            Format code"
	@echo "  lint           Lint code (requires golangci-lint)"
	@echo "  tidy           Tidy dependencies"
	@echo "  deps           Download dependencies"
	@echo "  clean          Clean build artifacts"
	@echo "  install-tools  Install development tools"
	@echo "  check-go       Check Go installation"
	@echo "  setup          Setup development environment"
	@echo "  test-backend   Test backend endpoints"
	@echo "  docker-build   Build Docker image"
	@echo "  help           Show this help message"
	@echo ""
	@echo "🚀 Quick start:"
	@echo "  make setup     # Setup development environment"
	@echo "  make build     # Build the application"
	@echo "  make run       # Run the application"
