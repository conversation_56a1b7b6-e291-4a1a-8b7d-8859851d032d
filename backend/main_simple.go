package main

import (
	"fmt"
	"log"

	"football-ai-backend/handlers"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("🚀 Starting DOS-Chat Backend Server...")

	// Create Gin router
	r := gin.Default()

	// Configure CORS
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:5173", "http://localhost:3000"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(config))

	// Health check endpoint
	r.GET("/health", handlers.HandleHealthCheck)

	// Main chat endpoint
	r.POST("/ask", handlers.HandleChatRequest)

	// Start server
	fmt.Println("🌐 Server running on http://localhost:8000")
	fmt.Println("📡 CORS enabled for frontend origins")
	fmt.Println("🎯 Endpoints:")
	fmt.Println("   GET  /health - Health check")
	fmt.Println("   POST /ask    - Chat API")
	fmt.Println("✅ Server ready to accept requests!")
	fmt.Println("=" + strings.Repeat("=", 50))

	if err := r.Run(":8000"); err != nil {
		log.Fatal("❌ Failed to start server:", err)
	}
}
