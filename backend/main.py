from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import json
import os
import re
from datetime import datetime
import hashlib

class Message(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    query: str
    language: Optional[str] = "en"
    messages: List[Message] = []

app = FastAPI()

# CORS 미들웨어 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 모든 출처 허용 (개발용)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

@app.get("/")
async def read_root():
    return {"message": "Legal AI Backend is running!"}

@app.get("/api/health")
async def health_check():
    return {"status": "ok"}

@app.options("/ask")
async def options_ask():
    return {"status": "ok"}

@app.post("/ask")
async def ask_question(chat_request: dict):
    try:
        print(f"Received request: {chat_request}")
        
        # 요청에서 필요한 데이터 추출
        query = chat_request.get("query", "").lower()
        language = chat_request.get("language", "en")
        messages = chat_request.get("messages", [])
        
        print(f"Processing query: {query}")
        print(f"Language: {language}")
        print(f"Previous messages: {len(messages)}")
        
        # 간단한 규칙 기반 응답 시스템
        response_text = generate_response(query, language)
        
        # 응답 생성
        response_message = {
            "response": response_text,
            "status": "success",
            "language": language
        }
        
        return response_message
        
    except Exception as e:
        print(f"Error processing request: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "status": "error",
                "message": "An error occurred while processing your request."
            }
        )

def load_football_rules() -> Dict[str, Any]:
    """Load and parse football rules from file"""
    rules_file = "mcp_data/football.txt"
    if not os.path.exists(rules_file):
        return {}
    
    with open(rules_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse rules using regex
    rules = {}
    current_section = None
    current_subsection = None
    
    for line in content.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        # Section header (e.g., ## 1. The Field of Play)
        if line.startswith('## '):
            current_section = line[3:].strip()
            rules[current_section] = {
                'title': current_section,
                'subsections': [],
                'keywords': set()
            }
            # Extract keywords from section title
            rules[current_section]['keywords'].update(current_section.lower().split())
        
        # Subsection header (e.g., ### 1.1 Dimensions)
        elif line.startswith('### '):
            current_subsection = line[4:].strip()
            if current_section:
                rules[current_section]['subsections'].append({
                    'title': current_subsection,
                    'content': [],
                    'keywords': set()
                })
                # Extract keywords from subsection title
                rules[current_section]['subsections'][-1]['keywords'].update(current_subsection.lower().split())
        
        # Rule content (indented lines)
        elif line.startswith('- '):
            if current_section and current_subsection:
                content_line = line[2:].strip()
                rules[current_section]['subsections'][-1]['content'].append(content_line)
                # Extract keywords from content
                rules[current_section]['subsections'][-1]['keywords'].update(content_line.lower().split())
                rules[current_section]['keywords'].update(content_line.lower().split())
    
    return rules

def generate_response(query: str, language: str = 'en') -> str:
    """Generate a response based on the user's query using football rules"""
    # Convert query to lowercase for case-insensitive matching
    query = query.lower()
    
    # Load football rules
    rules = load_football_rules()
    
    # Try to find matching section or rule
    response = ""
    best_match = None
    best_match_score = 0
    
    # Find the most relevant section
    for section, section_data in rules.items():
        # Calculate match score based on keyword overlap
        section_score = len(section_data['keywords'].intersection(query.split()))
        
        # Check subsections
        for subsection in section_data['subsections']:
            subsection_score = len(subsection['keywords'].intersection(query.split()))
            
            # Update best match if this subsection is more relevant
            if subsection_score > best_match_score:
                best_match = {
                    'title': f"{section_data['title']} - {subsection['title']}",
                    'content': subsection['content']
                }
                best_match_score = subsection_score
        
        # Update best match if section is more relevant than any subsection
        if section_score > best_match_score:
            best_match = {
                'title': section_data['title'],
                'content': [item for subsection in section_data['subsections'] for item in subsection['content']]
            }
            best_match_score = section_score
    
    # If we found a relevant match
    if best_match and best_match_score > 0:
        response = f"{best_match['title']}:\n\n"
        for content in best_match['content']:
            response += f"- {content}\n"
        return response
    
    # If no match found, use default responses
    responses = {
        'en': {
            'offside': "The offside rule in football states that a player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them. However, it's not an offense to be in an offside position unless the player becomes involved in active play.",
            'hello': "Hello! How can I assist you with football rules today?",
            'hi': "Hi there! What would you like to know about football?",
            'help': "I can help explain football rules and answer your questions. Try asking about specific rules like 'offside', 'penalty', or 'corner kick'.",
            'thank': "You're welcome! Feel free to ask if you have more questions about football.",
            'bye': "Goodbye! Feel free to come back if you have more questions about football rules!"
        },
        'ko': {
            'offside': "축구의 오프사이드 규칙은 공이 전달되는 순간, 공보다 상대편 골라인에 더 가까이 있고, 공과 상대편 선수들 중 두 번째로 마지막 상대 선수보다 골라인에 더 가까이 있는 선수가 오프사이드 위치에 있게 됩니다. 다만, 오프사이드 위치에 있는 것 자체가 반칙이 아니라, 그 선수가 실제 경기에 관여할 때만 반칙이 선언됩니다.",
            'hello': "안녕하세요! 축구 규칙에 대해 무엇이든 물어보세요!",
            'hi': "안녕하세요! 축구에 대해 무엇이 궁금하신가요?",
            'help': "축구 규칙에 대해 도와드릴게요. '오프사이드', '페널티킥', '코너킥' 등에 대해 물어보세요.",
            'thank': "천만에요! 축구 규칙에 대해 더 궁금한 점이 있으시면 언제든지 물어보세요.",
            'bye': "안녕히 가세요! 축구 규칙에 대해 더 궁금한 점이 있으시면 언제든지 다시 오세요!"
        }
    }
    
    lang_responses = responses.get(language, responses['en'])
    
    # Check for keywords in the query and return appropriate response
    for keyword, response_text in lang_responses.items():
        if keyword in query:
            return response_text
    
    # Default response if no keywords match
    default_responses = {
        'en': "I'm not sure I understand. Could you ask about a specific football rule? For example, you could ask 'What is offside?' or 'Explain the penalty kick rule.'",
        'ko': "이해하지 못했어요. 특정 축구 규칙에 대해 물어보시겠어요? 예를 들어 '오프사이드가 뭐예요?' 또는 '페널티킥 규칙을 알려주세요'라고 물어보실 수 있어요."
    }
    
    return default_responses.get(language, default_responses['en'])
