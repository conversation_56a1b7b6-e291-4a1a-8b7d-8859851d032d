package main

import (
	"fmt"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
)

type SimpleRequest struct {
	Query    string `json:"query"`
	Language string `json:"language"`
}

type SimpleResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

func main() {
	fmt.Println("🚀 Starting Simple Football AI Backend...")
	
	// Set debug mode
	gin.SetMode(gin.DebugMode)
	fmt.Println("✓ Gin debug mode set")
	
	// Create router
	router := gin.Default()
	fmt.Println("✓ Router created")
	
	// Add simple route
	router.POST("/ask", func(c *gin.Context) {
		fmt.Println("📥 Received request to /ask")
		
		var req SimpleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fmt.Printf("❌ JSON binding error: %v\n", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		
		fmt.Printf("📝 Query: %s, Language: %s\n", req.Query, req.Language)
		
		// Simple response logic
		var responseText string
		query := req.Query
		
		if contains(query, "offside") || contains(query, "off-side") {
			responseText = "Offside rule: A player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them."
		} else if contains(query, "penalty") {
			responseText = "Penalty kick rule: Awarded for direct free kick offenses in the penalty area. Ball placed on the penalty mark. Only the kicker and defending goalkeeper allowed in the penalty area."
		} else {
			responseText = "I can help explain football rules. Try asking about 'offside' or 'penalty kick'."
		}
		
		response := SimpleResponse{
			Response: responseText,
			Status:   "success",
			Language: req.Language,
		}
		
		fmt.Printf("📤 Sending response: %s\n", responseText[:min(50, len(responseText))])
		c.JSON(http.StatusOK, response)
	})
	
	// Health check
	router.GET("/api/health", func(c *gin.Context) {
		fmt.Println("📥 Health check request")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// Get port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}
	
	fmt.Printf("🌐 Starting server on port %s\n", port)
	if err := router.Run(":" + port); err != nil {
		fmt.Printf("❌ Failed to start server: %v\n", err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
