package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type SimpleRequest struct {
	Query    string `json:"query"`
	Language string `json:"language"`
}

type SimpleResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

func main() {
	fmt.Println("🚀 Starting Simple Football AI Backend...")
	
	// Set debug mode
	gin.SetMode(gin.DebugMode)
	fmt.Println("✓ Gin debug mode set")
	
	// Create router
	router := gin.Default()
	fmt.Println("✓ Router created")
	
	// Add simple route
	router.POST("/ask", func(c *gin.Context) {
		fmt.Println("📥 Received request to /ask")
		
		var req SimpleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fmt.Printf("❌ JSON binding error: %v\n", err)
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		
		fmt.Printf("📝 Query: %s, Language: %s\n", req.Query, req.Language)
		
		// Enhanced response logic for general AI assistant
		var responseText string
		query := strings.ToLower(req.Query)

		// Football rules
		if contains(query, "offside") || contains(query, "off-side") {
			responseText = "Offside rule: A player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them."
		} else if contains(query, "penalty") {
			responseText = "Penalty kick rule: Awarded for direct free kick offenses in the penalty area. Ball placed on the penalty mark. Only the kicker and defending goalkeeper allowed in the penalty area."

		// General greetings
		} else if contains(query, "hello") || contains(query, "hi") || contains(query, "안녕") {
			responseText = "Hello! I'm DOS-Chat, your AI assistant. I can help with various topics including sports rules, general knowledge, and more. What would you like to know?"

		// Programming questions
		} else if contains(query, "programming") || contains(query, "code") || contains(query, "python") || contains(query, "javascript") || contains(query, "go") {
			responseText = "I can help with programming questions! Whether it's Python, JavaScript, Go, or other languages, feel free to ask about syntax, concepts, or best practices."

		// Math questions
		} else if contains(query, "math") || contains(query, "calculate") || contains(query, "equation") {
			responseText = "I can help with mathematical problems and calculations. Please provide the specific math question you'd like assistance with."

		// Science questions
		} else if contains(query, "science") || contains(query, "physics") || contains(query, "chemistry") || contains(query, "biology") {
			responseText = "I can assist with science topics including physics, chemistry, biology, and more. What specific scientific concept would you like to explore?"

		// Technology questions
		} else if contains(query, "technology") || contains(query, "computer") || contains(query, "software") || contains(query, "hardware") {
			responseText = "I can help with technology-related questions about computers, software, hardware, and digital trends. What tech topic interests you?"

		// History questions
		} else if contains(query, "history") || contains(query, "historical") || contains(query, "past") {
			responseText = "I can discuss historical events, figures, and periods. What aspect of history would you like to learn about?"

		// Help/assistance
		} else if contains(query, "help") || contains(query, "assist") || contains(query, "도움") {
			responseText = "I'm here to help! I can assist with various topics including:\n• Sports rules and regulations\n• Programming and technology\n• Math and science\n• History and general knowledge\n• And much more! Just ask me anything."

		// Thank you
		} else if contains(query, "thank") || contains(query, "thanks") || contains(query, "감사") {
			responseText = "You're welcome! I'm happy to help. Feel free to ask me anything else you'd like to know."

		// Default response - try web search for unknown queries
		} else {
			fmt.Printf("🔍 Searching web for: %s\n", req.Query)
			searchResult, err := searchDuckDuckGo(req.Query)
			if err != nil {
				fmt.Printf("❌ Web search failed: %v\n", err)
				responseText = "I'm DOS-Chat, your AI assistant! I can help with various topics including sports, programming, science, math, history, and general knowledge. What would you like to know about?"
			} else {
				responseText = fmt.Sprintf("🌐 Here's what I found about '%s':\n\n%s", req.Query, searchResult)
			}
		}
		
		response := SimpleResponse{
			Response: responseText,
			Status:   "success",
			Language: req.Language,
		}
		
		fmt.Printf("📤 Sending response: %s\n", responseText[:min(50, len(responseText))])
		c.JSON(http.StatusOK, response)
	})
	
	// Health check
	router.GET("/api/health", func(c *gin.Context) {
		fmt.Println("📥 Health check request")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// Get port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}
	
	fmt.Printf("🌐 Starting server on port %s\n", port)
	if err := router.Run(":" + port); err != nil {
		fmt.Printf("❌ Failed to start server: %v\n", err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// searchDuckDuckGo performs a web search using DuckDuckGo
func searchDuckDuckGo(query string) (string, error) {
	// Use DuckDuckGo Instant Answer API (free, no API key required)
	baseURL := "https://api.duckduckgo.com/"
	params := url.Values{}
	params.Add("q", query)
	params.Add("format", "json")
	params.Add("no_html", "1")
	params.Add("skip_disambig", "1")

	searchURL := baseURL + "?" + params.Encode()

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Get(searchURL)
	if err != nil {
		return "", fmt.Errorf("failed to search: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// Parse JSON response
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Extract relevant information
	var searchResult strings.Builder

	// Check for instant answer
	if abstract, ok := result["Abstract"].(string); ok && abstract != "" {
		searchResult.WriteString(fmt.Sprintf("📝 %s\n\n", abstract))
	}

	// Check for definition
	if definition, ok := result["Definition"].(string); ok && definition != "" {
		searchResult.WriteString(fmt.Sprintf("📖 Definition: %s\n\n", definition))
	}

	// Check for answer
	if answer, ok := result["Answer"].(string); ok && answer != "" {
		searchResult.WriteString(fmt.Sprintf("💡 %s\n\n", answer))
	}

	// Check for related topics
	if relatedTopics, ok := result["RelatedTopics"].([]interface{}); ok && len(relatedTopics) > 0 {
		searchResult.WriteString("🔗 Related information:\n")
		for i, topic := range relatedTopics {
			if i >= 3 { // Limit to 3 related topics
				break
			}
			if topicMap, ok := topic.(map[string]interface{}); ok {
				if text, ok := topicMap["Text"].(string); ok && text != "" {
					searchResult.WriteString(fmt.Sprintf("• %s\n", text))
				}
			}
		}
	}

	result_text := searchResult.String()
	if result_text == "" {
		return fmt.Sprintf("I searched for '%s' but couldn't find specific information. You might want to try a more specific question or search directly on the web.", query), nil
	}

	return result_text, nil
}
