package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type SimpleRequest struct {
	Query    string `json:"query"`
	Language string `json:"language"`
}

type SimpleResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

func main() {
	fmt.Println("🚀 Starting Simple Football AI Backend...")
	
	// Set debug mode
	gin.SetMode(gin.DebugMode)
	fmt.Println("✓ Gin debug mode set")
	
	// Create router
	router := gin.Default()
	fmt.Println("✓ Router created")
	
	// Add simple route
	router.POST("/ask", func(c *gin.Context) {
		fmt.Println("📥 Received request to /ask")
		
		var req SimpleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fmt.Printf("❌ JSON binding error: %v\n", err)
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		
		fmt.Printf("📝 Query: %s, Language: %s\n", req.Query, req.Language)
		
		// Enhanced response logic for general AI assistant
		var responseText string
		query := strings.ToLower(req.Query)

		// Football rules
		if contains(query, "offside") || contains(query, "off-side") {
			responseText = "Offside rule: A player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them."
		} else if contains(query, "penalty") {
			responseText = "Penalty kick rule: Awarded for direct free kick offenses in the penalty area. Ball placed on the penalty mark. Only the kicker and defending goalkeeper allowed in the penalty area."

		// General greetings
		} else if contains(query, "hello") || contains(query, "hi") || contains(query, "안녕") || contains(query, "hola") || contains(query, "bonjour") || contains(query, "hallo") || contains(query, "merhaba") || contains(query, "你好") {
			switch req.Language {
			case "ko":
				responseText = "안녕하세요! 저는 DOS-Chat, 여러분의 AI 어시스턴트입니다. 스포츠 규칙, 일반 상식 등 다양한 주제에 대해 도움을 드릴 수 있어요. 무엇이 궁금하신가요?"
			case "es":
				responseText = "¡Hola! Soy DOS-Chat, tu asistente de IA. Puedo ayudarte con varios temas incluyendo reglas deportivas, conocimiento general y más. ¿Qué te gustaría saber?"
			case "fr":
				responseText = "Bonjour ! Je suis DOS-Chat, votre assistant IA. Je peux vous aider avec divers sujets incluant les règles sportives, les connaissances générales et plus encore. Que souhaitez-vous savoir ?"
			case "de":
				responseText = "Hallo! Ich bin DOS-Chat, Ihr KI-Assistent. Ich kann Ihnen bei verschiedenen Themen helfen, einschließlich Sportregeln, Allgemeinwissen und mehr. Was möchten Sie wissen?"
			case "tr":
				responseText = "Merhaba! Ben DOS-Chat, sizin AI asistanınızım. Spor kuralları, genel bilgi ve daha fazlası dahil olmak üzere çeşitli konularda yardımcı olabilirim. Ne öğrenmek istersiniz?"
			case "zh":
				responseText = "你好！我是DOS-Chat，您的AI助手。我可以帮助您了解各种主题，包括体育规则、常识等。您想了解什么？"
			default:
				responseText = "Hello! I'm DOS-Chat, your AI assistant. I can help with various topics including sports rules, general knowledge, and more. What would you like to know?"
			}

		// Programming questions
		} else if contains(query, "programming") || contains(query, "code") || contains(query, "python") || contains(query, "javascript") || contains(query, "go") || contains(query, "프로그래밍") || contains(query, "코딩") || contains(query, "파이썬") {
			if req.Language == "ko" {
				responseText = "프로그래밍 질문에 도움을 드릴 수 있어요! Python, JavaScript, Go 등 다양한 언어의 문법, 개념, 모범 사례에 대해 자유롭게 질문해주세요."
			} else {
				responseText = "I can help with programming questions! Whether it's Python, JavaScript, Go, or other languages, feel free to ask about syntax, concepts, or best practices."
			}

		// Math questions
		} else if contains(query, "math") || contains(query, "calculate") || contains(query, "equation") {
			responseText = "I can help with mathematical problems and calculations. Please provide the specific math question you'd like assistance with."

		// Science questions
		} else if contains(query, "science") || contains(query, "physics") || contains(query, "chemistry") || contains(query, "biology") {
			responseText = "I can assist with science topics including physics, chemistry, biology, and more. What specific scientific concept would you like to explore?"

		// Technology questions
		} else if contains(query, "technology") || contains(query, "computer") || contains(query, "software") || contains(query, "hardware") {
			responseText = "I can help with technology-related questions about computers, software, hardware, and digital trends. What tech topic interests you?"

		// History questions
		} else if contains(query, "history") || contains(query, "historical") || contains(query, "past") {
			responseText = "I can discuss historical events, figures, and periods. What aspect of history would you like to learn about?"

		// Help/assistance
		} else if contains(query, "help") || contains(query, "assist") || contains(query, "도움") || contains(query, "ayuda") || contains(query, "aide") || contains(query, "hilfe") || contains(query, "yardım") || contains(query, "帮助") {
			switch req.Language {
			case "ko":
				responseText = "도움을 드리러 왔어요! 다음과 같은 주제들에 대해 도움을 드릴 수 있습니다:\n• 스포츠 규칙 및 규정\n• 프로그래밍 및 기술\n• 수학 및 과학\n• 역사 및 일반 상식\n• 그리고 더 많은 것들! 무엇이든 물어보세요."
			case "es":
				responseText = "¡Estoy aquí para ayudar! Puedo asistirte con varios temas incluyendo:\n• Reglas y regulaciones deportivas\n• Programación y tecnología\n• Matemáticas y ciencias\n• Historia y conocimiento general\n• ¡Y mucho más! Solo pregúntame cualquier cosa."
			case "fr":
				responseText = "Je suis là pour vous aider ! Je peux vous assister avec divers sujets incluant :\n• Règles et réglementations sportives\n• Programmation et technologie\n• Mathématiques et sciences\n• Histoire et connaissances générales\n• Et bien plus encore ! Posez-moi n'importe quelle question."
			case "de":
				responseText = "Ich bin hier, um zu helfen! Ich kann Sie bei verschiedenen Themen unterstützen:\n• Sportregeln und -vorschriften\n• Programmierung und Technologie\n• Mathematik und Wissenschaft\n• Geschichte und Allgemeinwissen\n• Und vieles mehr! Fragen Sie mich einfach alles."
			case "tr":
				responseText = "Yardım etmek için buradayım! Çeşitli konularda size yardımcı olabilirim:\n• Spor kuralları ve düzenlemeleri\n• Programlama ve teknoloji\n• Matematik ve bilim\n• Tarih ve genel bilgi\n• Ve çok daha fazlası! Bana her şeyi sorabilirsiniz."
			case "zh":
				responseText = "我来帮助您！我可以协助您处理各种主题，包括：\n• 体育规则和法规\n• 编程和技术\n• 数学和科学\n• 历史和常识\n• 还有更多！请随时问我任何问题。"
			default:
				responseText = "I'm here to help! I can assist with various topics including:\n• Sports rules and regulations\n• Programming and technology\n• Math and science\n• History and general knowledge\n• And much more! Just ask me anything."
			}

		// Thank you
		} else if contains(query, "thank") || contains(query, "thanks") || contains(query, "감사") || contains(query, "gracias") || contains(query, "merci") || contains(query, "danke") || contains(query, "teşekkür") || contains(query, "谢谢") {
			switch req.Language {
			case "ko":
				responseText = "천만에요! 도움이 되어서 기뻐요. 다른 궁금한 것이 있으시면 언제든 물어보세요."
			case "es":
				responseText = "¡De nada! Me alegra poder ayudar. Siéntete libre de preguntarme cualquier otra cosa que quieras saber."
			case "fr":
				responseText = "De rien ! Je suis heureux de pouvoir vous aider. N'hésitez pas à me poser toute autre question."
			case "de":
				responseText = "Gern geschehen! Ich freue mich, helfen zu können. Fragen Sie gerne alles andere, was Sie wissen möchten."
			case "tr":
				responseText = "Rica ederim! Yardımcı olabildiğim için mutluyum. Bilmek istediğiniz başka bir şey varsa çekinmeden sorun."
			case "zh":
				responseText = "不客气！我很高兴能帮助您。如果您还有其他想了解的问题，请随时询问。"
			default:
				responseText = "You're welcome! I'm happy to help. Feel free to ask me anything else you'd like to know."
			}

		// Default response - try web search for unknown queries
		} else {
			// Translate Korean query to English for better search results
			searchQuery := translateKoreanToEnglish(req.Query)
			fmt.Printf("🔍 Searching web for: %s (translated from: %s)\n", searchQuery, req.Query)

			searchResult, err := searchDuckDuckGo(searchQuery)
			if err != nil {
				fmt.Printf("❌ Web search failed: %v\n", err)
				switch req.Language {
				case "ko":
					responseText = "저는 DOS-Chat, 여러분의 AI 어시스턴트입니다! 스포츠, 프로그래밍, 과학, 수학, 역사, 일반 상식 등 다양한 주제에 대해 도움을 드릴 수 있어요. 무엇이 궁금하신가요?"
				case "es":
					responseText = "¡Soy DOS-Chat, tu asistente de IA! Puedo ayudarte con varios temas incluyendo deportes, programación, ciencias, matemáticas, historia y conocimiento general. ¿Sobre qué te gustaría saber?"
				case "fr":
					responseText = "Je suis DOS-Chat, votre assistant IA ! Je peux vous aider avec divers sujets incluant le sport, la programmation, les sciences, les mathématiques, l'histoire et les connaissances générales. Sur quoi souhaitez-vous en savoir plus ?"
				case "de":
					responseText = "Ich bin DOS-Chat, Ihr KI-Assistent! Ich kann Ihnen bei verschiedenen Themen helfen, einschließlich Sport, Programmierung, Wissenschaft, Mathematik, Geschichte und Allgemeinwissen. Worüber möchten Sie mehr erfahren?"
				case "tr":
					responseText = "Ben DOS-Chat, sizin AI asistanınızım! Spor, programlama, bilim, matematik, tarih ve genel bilgi dahil olmak üzere çeşitli konularda yardımcı olabilirim. Ne hakkında bilgi almak istersiniz?"
				case "zh":
					responseText = "我是DOS-Chat，您的AI助手！我可以帮助您了解各种主题，包括体育、编程、科学、数学、历史和常识。您想了解什么？"
				default:
					responseText = "I'm DOS-Chat, your AI assistant! I can help with various topics including sports, programming, science, math, history, and general knowledge. What would you like to know about?"
				}
			} else {
				switch req.Language {
				case "ko":
					// Create Korean summary from search result
					koreanSummary := createKoreanSummary(searchResult, req.Query)
					responseText = fmt.Sprintf("🌐 '%s'에 대해 찾은 정보입니다:\n\n%s", req.Query, koreanSummary)
				case "es":
					responseText = fmt.Sprintf("🌐 Esto es lo que encontré sobre '%s':\n\n%s", req.Query, searchResult)
				case "fr":
					responseText = fmt.Sprintf("🌐 Voici ce que j'ai trouvé sur '%s':\n\n%s", req.Query, searchResult)
				case "de":
					responseText = fmt.Sprintf("🌐 Das habe ich über '%s' gefunden:\n\n%s", req.Query, searchResult)
				case "tr":
					responseText = fmt.Sprintf("🌐 '%s' hakkında bulduklarım:\n\n%s", req.Query, searchResult)
				case "zh":
					responseText = fmt.Sprintf("🌐 关于'%s'我找到的信息：\n\n%s", req.Query, searchResult)
				default:
					responseText = fmt.Sprintf("🌐 Here's what I found about '%s':\n\n%s", req.Query, searchResult)
				}
			}
		}
		
		response := SimpleResponse{
			Response: responseText,
			Status:   "success",
			Language: req.Language,
		}
		
		fmt.Printf("📤 Sending response: %s\n", responseText[:min(50, len(responseText))])
		c.JSON(http.StatusOK, response)
	})
	
	// Health check
	router.GET("/api/health", func(c *gin.Context) {
		fmt.Println("📥 Health check request")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// Get port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}
	
	fmt.Printf("🌐 Starting server on port %s\n", port)
	if err := router.Run(":" + port); err != nil {
		fmt.Printf("❌ Failed to start server: %v\n", err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// translateKoreanToEnglish translates common Korean names and terms to English for better search results
func translateKoreanToEnglish(query string) string {
	translations := map[string]string{
		"엘론 머스크":   "Elon Musk",
		"일론 머스크":   "Elon Musk",
		"도널드 트럼프":  "Donald Trump",
		"트럼프":     "Trump",
		"빌 게이츠":   "Bill Gates",
		"스티브 잡스":  "Steve Jobs",
		"마크 저커버그": "Mark Zuckerberg",
		"제프 베조스":  "Jeff Bezos",
		"워렌 버핏":   "Warren Buffett",
		"테슬라":     "Tesla",
		"스페이스X":   "SpaceX",
		"애플":      "Apple",
		"구글":      "Google",
		"마이크로소프트": "Microsoft",
		"아마존":     "Amazon",
		"페이스북":    "Facebook",
		"메타":      "Meta",
		"넷플릭스":    "Netflix",
		"삼성":      "Samsung",
		"LG":       "LG",
		"현대":      "Hyundai",
		"기아":      "Kia",
	}

	// First, translate Korean names to English
	translatedQuery := query
	for korean, english := range translations {
		if contains(translatedQuery, korean) {
			// Replace the entire Korean name with English name
			translatedQuery = strings.ReplaceAll(translatedQuery, korean, english)
		}
	}

	// Additional cleanup for partial matches
	if contains(translatedQuery, "도널드") && contains(translatedQuery, "Trump") {
		translatedQuery = strings.ReplaceAll(translatedQuery, "도널드", "Donald")
	}

	// Remove Korean question patterns and keep only the essential part
	questionPatterns := []string{
		"가 누구야?", "가 누구인가요?", "가 누구지?", "가 누구예요?",
		"는 누구야?", "는 누구인가요?", "는 누구지?", "는 누구예요?",
		"이 누구야?", "이 누구인가요?", "이 누구지?", "이 누구예요?",
		"에 대해 알려줘", "에 대해서 알려줘", "에 대해 설명해줘",
		"가 뭐야?", "가 무엇인가요?", "는 뭐야?", "는 무엇인가요?",
		"이 뭐야?", "이 무엇인가요?",
	}

	for _, pattern := range questionPatterns {
		if strings.Contains(translatedQuery, pattern) {
			translatedQuery = strings.ReplaceAll(translatedQuery, pattern, "")
			break
		}
	}

	// Clean up extra spaces
	translatedQuery = strings.TrimSpace(translatedQuery)

	// If the query is empty after cleaning, return the original
	if translatedQuery == "" {
		return query
	}

	return translatedQuery
}

// translateEnglishToKorean translates common English terms to Korean for better user experience
func translateEnglishToKorean(text string) string {
	translations := map[string]string{
		"businessman":     "사업가",
		"politician":      "정치인",
		"president":       "대통령",
		"CEO":            "최고경영자",
		"founder":        "창립자",
		"co-founder":     "공동창립자",
		"entrepreneur":   "기업가",
		"billionaire":    "억만장자",
		"company":        "회사",
		"corporation":    "기업",
		"technology":     "기술",
		"innovation":     "혁신",
		"electric":       "전기",
		"vehicle":        "차량",
		"space":          "우주",
		"rocket":         "로켓",
		"university":     "대학교",
		"graduated":      "졸업했습니다",
		"bachelor":       "학사",
		"degree":         "학위",
		"economics":      "경제학",
		"real estate":    "부동산",
		"business":       "사업",
		"hotel":          "호텔",
		"casino":         "카지노",
		"golf course":    "골프장",
		"television":     "텔레비전",
		"show":           "쇼",
		"reality":        "리얼리티",
		"election":       "선거",
		"Republican":     "공화당",
		"Democratic":     "민주당",
		"Party":          "당",
		"Tesla":          "테슬라",
		"SpaceX":         "스페이스X",
		"PayPal":         "페이팔",
		"Twitter":        "트위터",
		"X":              "X",
		"born":           "태어났습니다",
		"family":         "가족",
		"wealthy":        "부유한",
		"South Africa":   "남아프리카",
		"Canada":         "캐나다",
		"United States":  "미국",
		"California":     "캘리포니아",
		"Pennsylvania":   "펜실베이니아",
		"Queens":         "퀸즈",
		"New York":       "뉴욕",
		"net worth":      "순자산",
		"billion":        "억",
		"million":        "백만",
		"dollars":        "달러",
		"USD":            "미국 달러",
		"Forbes":         "포브스",
		"estimates":      "추정합니다",
		"leadership":     "리더십",
		"chief engineer": "최고기술책임자",
		"reusable":       "재사용 가능한",
		"commercial":     "상업적",
		"spaceflight":    "우주비행",
		"innovations":    "혁신들",
		"acquired":       "인수했습니다",
		"merged":         "합병했습니다",
		"citizen":        "시민",
		"American":       "미국인",
		"emigrated":      "이민했습니다",
		"ventures":       "벤처",
		"pursue":         "추구하기 위해",
		"software":       "소프트웨어",
		"online":         "온라인",
		"payment":        "결제",
		"eBay":           "이베이",
		"media":          "미디어",
		"personality":    "인물",
		"member":         "구성원",
		"served":         "재직했습니다",
		"skyscrapers":    "고층빌딩",
		"licensing":      "라이선싱",
		"bankruptcies":   "파산",
		"hosted":         "진행했습니다",
		"bolstering":     "강화하며",
		"fame":           "명성",
		"presenting":     "자신을 내세우며",
		"outsider":       "아웃사이더",
		"nominee":        "후보",
		"Hillary Clinton": "힐러리 클린턴",
		"The Apprentice": "어프렌티스",
		"Trump Organization": "트럼프 오거나이제이션",
	}

	result := text
	for english, korean := range translations {
		// Case-insensitive replacement
		result = strings.ReplaceAll(result, english, korean)
		result = strings.ReplaceAll(result, strings.ToLower(english), korean)
		result = strings.ReplaceAll(result, strings.ToUpper(english), korean)
		result = strings.ReplaceAll(result, strings.Title(english), korean)
	}

	return result
}

// createKoreanSummary creates a Korean summary from English search results
func createKoreanSummary(englishText, queryName string) string {
	// Extract key information and create Korean summary
	var summary strings.Builder

	// Check if it's about a person
	if contains(englishText, "businessman") || contains(englishText, "politician") || contains(englishText, "CEO") {
		summary.WriteString(fmt.Sprintf("📝 %s에 대한 정보:\n\n", queryName))

		// Extract profession
		if contains(englishText, "businessman") && contains(englishText, "politician") {
			summary.WriteString("• 직업: 사업가, 정치인\n")
		} else if contains(englishText, "businessman") {
			summary.WriteString("• 직업: 사업가\n")
		} else if contains(englishText, "politician") {
			summary.WriteString("• 직업: 정치인\n")
		}

		// Extract companies/roles
		if contains(englishText, "Tesla") {
			summary.WriteString("• 테슬라 CEO\n")
		}
		if contains(englishText, "SpaceX") {
			summary.WriteString("• 스페이스X 창립자 및 CEO\n")
		}
		if contains(englishText, "president") && contains(englishText, "United States") {
			if contains(englishText, "47th") {
				summary.WriteString("• 제47대 미국 대통령\n")
			}
			if contains(englishText, "45th") {
				summary.WriteString("• 제45대 미국 대통령 (2017-2021)\n")
			}
		}

		// Extract net worth
		if contains(englishText, "billion") && contains(englishText, "net worth") {
			if contains(englishText, "$424.7 billion") {
				summary.WriteString("• 순자산: 약 4,247억 달러 (2025년 기준)\n")
			}
		}

		// Extract birthplace
		if contains(englishText, "South Africa") {
			summary.WriteString("• 출생지: 남아프리카공화국\n")
		}
		if contains(englishText, "Queens") && contains(englishText, "New York") {
			summary.WriteString("• 출생지: 뉴욕 퀸즈\n")
		}

		// Extract education
		if contains(englishText, "University of Pennsylvania") {
			summary.WriteString("• 학력: 펜실베이니아 대학교 졸업\n")
		}

		// Extract notable achievements
		if contains(englishText, "PayPal") {
			summary.WriteString("• 페이팔 공동창립자\n")
		}
		if contains(englishText, "The Apprentice") {
			summary.WriteString("• 리얼리티 쇼 '어프렌티스' 진행 (2004-2015)\n")
		}
		if contains(englishText, "reusable rockets") {
			summary.WriteString("• 재사용 로켓 기술 혁신 선도\n")
		}

		summary.WriteString("\n💡 세계에서 가장 영향력 있는 인물 중 한 명으로 평가받고 있습니다.")

	} else {
		// For non-person queries, use simple translation
		summary.WriteString(translateEnglishToKorean(englishText))
	}

	return summary.String()
}

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// searchDuckDuckGo performs a web search using DuckDuckGo
func searchDuckDuckGo(query string) (string, error) {
	// Use DuckDuckGo Instant Answer API (free, no API key required)
	baseURL := "https://api.duckduckgo.com/"
	params := url.Values{}
	params.Add("q", query)
	params.Add("format", "json")
	params.Add("no_html", "1")
	params.Add("skip_disambig", "1")

	searchURL := baseURL + "?" + params.Encode()

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Get(searchURL)
	if err != nil {
		return "", fmt.Errorf("failed to search: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// Parse JSON response
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Extract relevant information
	var searchResult strings.Builder

	// Check for instant answer
	if abstract, ok := result["Abstract"].(string); ok && abstract != "" {
		searchResult.WriteString(fmt.Sprintf("📝 %s\n\n", abstract))
	}

	// Check for definition
	if definition, ok := result["Definition"].(string); ok && definition != "" {
		searchResult.WriteString(fmt.Sprintf("📖 Definition: %s\n\n", definition))
	}

	// Check for answer
	if answer, ok := result["Answer"].(string); ok && answer != "" {
		searchResult.WriteString(fmt.Sprintf("💡 %s\n\n", answer))
	}

	// Check for related topics
	if relatedTopics, ok := result["RelatedTopics"].([]interface{}); ok && len(relatedTopics) > 0 {
		searchResult.WriteString("🔗 Related information:\n")
		for i, topic := range relatedTopics {
			if i >= 3 { // Limit to 3 related topics
				break
			}
			if topicMap, ok := topic.(map[string]interface{}); ok {
				if text, ok := topicMap["Text"].(string); ok && text != "" {
					searchResult.WriteString(fmt.Sprintf("• %s\n", text))
				}
			}
		}
	}

	result_text := searchResult.String()
	if result_text == "" {
		return fmt.Sprintf("I searched for '%s' but couldn't find specific information. You might want to try a more specific question or search directly on the web.", query), nil
	}

	return result_text, nil
}
