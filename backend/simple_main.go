package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type SimpleRequest struct {
	Query    string `json:"query"`
	Language string `json:"language"`
}

type SimpleResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

func main() {
	fmt.Println("🚀 Starting Simple Football AI Backend...")
	
	// Set debug mode
	gin.SetMode(gin.DebugMode)
	fmt.Println("✓ Gin debug mode set")
	
	// Create router
	router := gin.Default()
	fmt.Println("✓ Router created")
	
	// Add simple route
	router.POST("/ask", func(c *gin.Context) {
		fmt.Println("📥 Received request to /ask")
		
		var req SimpleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fmt.Printf("❌ JSON binding error: %v\n", err)
			c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		
		fmt.Printf("📝 Query: %s, Language: %s\n", req.Query, req.Language)
		
		// Enhanced response logic for general AI assistant
		var responseText string
		query := strings.ToLower(req.Query)

		// Football rules
		if contains(query, "offside") || contains(query, "off-side") {
			responseText = "Offside rule: A player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them."
		} else if contains(query, "penalty") {
			responseText = "Penalty kick rule: Awarded for direct free kick offenses in the penalty area. Ball placed on the penalty mark. Only the kicker and defending goalkeeper allowed in the penalty area."

		// General greetings
		} else if contains(query, "hello") || contains(query, "hi") || contains(query, "안녕") {
			if req.Language == "ko" {
				responseText = "안녕하세요! 저는 DOS-Chat, 여러분의 AI 어시스턴트입니다. 스포츠 규칙, 일반 상식 등 다양한 주제에 대해 도움을 드릴 수 있어요. 무엇이 궁금하신가요?"
			} else {
				responseText = "Hello! I'm DOS-Chat, your AI assistant. I can help with various topics including sports rules, general knowledge, and more. What would you like to know?"
			}

		// Programming questions
		} else if contains(query, "programming") || contains(query, "code") || contains(query, "python") || contains(query, "javascript") || contains(query, "go") || contains(query, "프로그래밍") || contains(query, "코딩") || contains(query, "파이썬") {
			if req.Language == "ko" {
				responseText = "프로그래밍 질문에 도움을 드릴 수 있어요! Python, JavaScript, Go 등 다양한 언어의 문법, 개념, 모범 사례에 대해 자유롭게 질문해주세요."
			} else {
				responseText = "I can help with programming questions! Whether it's Python, JavaScript, Go, or other languages, feel free to ask about syntax, concepts, or best practices."
			}

		// Math questions
		} else if contains(query, "math") || contains(query, "calculate") || contains(query, "equation") {
			responseText = "I can help with mathematical problems and calculations. Please provide the specific math question you'd like assistance with."

		// Science questions
		} else if contains(query, "science") || contains(query, "physics") || contains(query, "chemistry") || contains(query, "biology") {
			responseText = "I can assist with science topics including physics, chemistry, biology, and more. What specific scientific concept would you like to explore?"

		// Technology questions
		} else if contains(query, "technology") || contains(query, "computer") || contains(query, "software") || contains(query, "hardware") {
			responseText = "I can help with technology-related questions about computers, software, hardware, and digital trends. What tech topic interests you?"

		// History questions
		} else if contains(query, "history") || contains(query, "historical") || contains(query, "past") {
			responseText = "I can discuss historical events, figures, and periods. What aspect of history would you like to learn about?"

		// Help/assistance
		} else if contains(query, "help") || contains(query, "assist") || contains(query, "도움") {
			if req.Language == "ko" {
				responseText = "도움을 드리러 왔어요! 다음과 같은 주제들에 대해 도움을 드릴 수 있습니다:\n• 스포츠 규칙 및 규정\n• 프로그래밍 및 기술\n• 수학 및 과학\n• 역사 및 일반 상식\n• 그리고 더 많은 것들! 무엇이든 물어보세요."
			} else {
				responseText = "I'm here to help! I can assist with various topics including:\n• Sports rules and regulations\n• Programming and technology\n• Math and science\n• History and general knowledge\n• And much more! Just ask me anything."
			}

		// Thank you
		} else if contains(query, "thank") || contains(query, "thanks") || contains(query, "감사") {
			if req.Language == "ko" {
				responseText = "천만에요! 도움이 되어서 기뻐요. 다른 궁금한 것이 있으시면 언제든 물어보세요."
			} else {
				responseText = "You're welcome! I'm happy to help. Feel free to ask me anything else you'd like to know."
			}

		// Default response - try web search for unknown queries
		} else {
			// Translate Korean query to English for better search results
			searchQuery := translateKoreanToEnglish(req.Query)
			fmt.Printf("🔍 Searching web for: %s (translated from: %s)\n", searchQuery, req.Query)

			searchResult, err := searchDuckDuckGo(searchQuery)
			if err != nil {
				fmt.Printf("❌ Web search failed: %v\n", err)
				if req.Language == "ko" {
					responseText = "저는 DOS-Chat, 여러분의 AI 어시스턴트입니다! 스포츠, 프로그래밍, 과학, 수학, 역사, 일반 상식 등 다양한 주제에 대해 도움을 드릴 수 있어요. 무엇이 궁금하신가요?"
				} else {
					responseText = "I'm DOS-Chat, your AI assistant! I can help with various topics including sports, programming, science, math, history, and general knowledge. What would you like to know about?"
				}
			} else {
				if req.Language == "ko" {
					responseText = fmt.Sprintf("🌐 '%s'에 대해 찾은 정보입니다:\n\n%s", req.Query, searchResult)
				} else {
					responseText = fmt.Sprintf("🌐 Here's what I found about '%s':\n\n%s", req.Query, searchResult)
				}
			}
		}
		
		response := SimpleResponse{
			Response: responseText,
			Status:   "success",
			Language: req.Language,
		}
		
		fmt.Printf("📤 Sending response: %s\n", responseText[:min(50, len(responseText))])
		c.JSON(http.StatusOK, response)
	})
	
	// Health check
	router.GET("/api/health", func(c *gin.Context) {
		fmt.Println("📥 Health check request")
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// Get port
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}
	
	fmt.Printf("🌐 Starting server on port %s\n", port)
	if err := router.Run(":" + port); err != nil {
		fmt.Printf("❌ Failed to start server: %v\n", err)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// translateKoreanToEnglish translates common Korean names and terms to English for better search results
func translateKoreanToEnglish(query string) string {
	translations := map[string]string{
		"엘론 머스크":   "Elon Musk",
		"일론 머스크":   "Elon Musk",
		"도널드 트럼프":  "Donald Trump",
		"트럼프":     "Trump",
		"빌 게이츠":   "Bill Gates",
		"스티브 잡스":  "Steve Jobs",
		"마크 저커버그": "Mark Zuckerberg",
		"제프 베조스":  "Jeff Bezos",
		"워렌 버핏":   "Warren Buffett",
		"테슬라":     "Tesla",
		"스페이스X":   "SpaceX",
		"애플":      "Apple",
		"구글":      "Google",
		"마이크로소프트": "Microsoft",
		"아마존":     "Amazon",
		"페이스북":    "Facebook",
		"메타":      "Meta",
		"넷플릭스":    "Netflix",
		"삼성":      "Samsung",
		"LG":       "LG",
		"현대":      "Hyundai",
		"기아":      "Kia",
	}

	// First, translate Korean names to English
	translatedQuery := query
	for korean, english := range translations {
		if contains(translatedQuery, korean) {
			// Replace the entire Korean name with English name
			translatedQuery = strings.ReplaceAll(translatedQuery, korean, english)
		}
	}

	// Additional cleanup for partial matches
	if contains(translatedQuery, "도널드") && contains(translatedQuery, "Trump") {
		translatedQuery = strings.ReplaceAll(translatedQuery, "도널드", "Donald")
	}

	// Remove Korean question patterns and keep only the essential part
	questionPatterns := []string{
		"가 누구야?", "가 누구인가요?", "가 누구지?", "가 누구예요?",
		"는 누구야?", "는 누구인가요?", "는 누구지?", "는 누구예요?",
		"이 누구야?", "이 누구인가요?", "이 누구지?", "이 누구예요?",
		"에 대해 알려줘", "에 대해서 알려줘", "에 대해 설명해줘",
		"가 뭐야?", "가 무엇인가요?", "는 뭐야?", "는 무엇인가요?",
		"이 뭐야?", "이 무엇인가요?",
	}

	for _, pattern := range questionPatterns {
		if strings.Contains(translatedQuery, pattern) {
			translatedQuery = strings.ReplaceAll(translatedQuery, pattern, "")
			break
		}
	}

	// Clean up extra spaces
	translatedQuery = strings.TrimSpace(translatedQuery)

	// If the query is empty after cleaning, return the original
	if translatedQuery == "" {
		return query
	}

	return translatedQuery
}

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// searchDuckDuckGo performs a web search using DuckDuckGo
func searchDuckDuckGo(query string) (string, error) {
	// Use DuckDuckGo Instant Answer API (free, no API key required)
	baseURL := "https://api.duckduckgo.com/"
	params := url.Values{}
	params.Add("q", query)
	params.Add("format", "json")
	params.Add("no_html", "1")
	params.Add("skip_disambig", "1")

	searchURL := baseURL + "?" + params.Encode()

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Get(searchURL)
	if err != nil {
		return "", fmt.Errorf("failed to search: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// Parse JSON response
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Extract relevant information
	var searchResult strings.Builder

	// Check for instant answer
	if abstract, ok := result["Abstract"].(string); ok && abstract != "" {
		searchResult.WriteString(fmt.Sprintf("📝 %s\n\n", abstract))
	}

	// Check for definition
	if definition, ok := result["Definition"].(string); ok && definition != "" {
		searchResult.WriteString(fmt.Sprintf("📖 Definition: %s\n\n", definition))
	}

	// Check for answer
	if answer, ok := result["Answer"].(string); ok && answer != "" {
		searchResult.WriteString(fmt.Sprintf("💡 %s\n\n", answer))
	}

	// Check for related topics
	if relatedTopics, ok := result["RelatedTopics"].([]interface{}); ok && len(relatedTopics) > 0 {
		searchResult.WriteString("🔗 Related information:\n")
		for i, topic := range relatedTopics {
			if i >= 3 { // Limit to 3 related topics
				break
			}
			if topicMap, ok := topic.(map[string]interface{}); ok {
				if text, ok := topicMap["Text"].(string); ok && text != "" {
					searchResult.WriteString(fmt.Sprintf("• %s\n", text))
				}
			}
		}
	}

	result_text := searchResult.String()
	if result_text == "" {
		return fmt.Sprintf("I searched for '%s' but couldn't find specific information. You might want to try a more specific question or search directly on the web.", query), nil
	}

	return result_text, nil
}
