# Football AI Backend (Go)

이 백엔드는 Go 언어로 작성되어 API 호출과 MCP tools 호출을 병렬로 처리할 수 있습니다.

## 🚀 주요 기능

- **병렬 처리**: Goroutine을 활용한 동시성 처리
- **축구 규칙 매칭**: 로컬 축구 규칙 데이터베이스 검색
- **MCP Tools 통합**: Model Context Protocol 도구들과의 병렬 통신
- **다국어 지원**: 영어/한국어 응답 지원
- **CORS 지원**: 프론트엔드와의 원활한 통신

## 📋 사전 요구사항

### Go 설치

Go 1.21 이상이 필요합니다.

#### macOS (Homebrew 사용)
```bash
brew install go
```

#### macOS (공식 설치 프로그램)
1. https://golang.org/dl/ 에서 macOS용 설치 프로그램 다운로드
2. 설치 프로그램 실행
3. 터미널을 재시작하고 `go version` 명령으로 확인

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install golang-go

# CentOS/RHEL
sudo yum install golang
```

#### Windows
1. https://golang.org/dl/ 에서 Windows용 설치 프로그램 다운로드
2. 설치 프로그램 실행
3. 명령 프롬프트를 재시작하고 `go version` 명령으로 확인

## 🔧 설치 및 실행

### 1. 의존성 설치
```bash
cd backend
go mod tidy
```

### 2. 애플리케이션 빌드
```bash
go build -o football-ai-backend .
```

### 3. 실행
```bash
./football-ai-backend
```

또는 빌드 없이 직접 실행:
```bash
go run main.go
```

### 4. 전체 애플리케이션 실행 (프론트엔드 포함)
루트 디렉토리에서:
```bash
./start.sh
```

## 🌐 API 엔드포인트

### 기본 엔드포인트
- `GET /` - 루트 엔드포인트 (서버 정보)
- `GET /api/health` - 헬스 체크

### 채팅 엔드포인트
- `POST /ask` - 채팅 질문 처리
- `OPTIONS /ask` - CORS preflight 요청

### MCP Tools 엔드포인트
- `GET /mcp/tools` - 사용 가능한 MCP 도구 목록
- `POST /mcp/tools` - MCP 도구 테스트

## 📊 API 사용 예시

### 채팅 요청
```bash
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is offside in football?",
    "language": "en"
  }'
```

### 한국어 요청
```bash
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{
    "query": "오프사이드가 뭐예요?",
    "language": "ko"
  }'
```

### MCP 도구 테스트
```bash
curl -X POST http://localhost:8000/mcp/tools \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "football-search",
    "query": "penalty kick rules"
  }'
```

## 🏗️ 아키텍처

```
backend/
├── main.go                 # 메인 애플리케이션 엔트리포인트
├── models/                 # 데이터 모델 정의
│   └── types.go
├── services/               # 비즈니스 로직 서비스
│   ├── football_rules.go   # 축구 규칙 파싱 및 매칭
│   ├── mcp_service.go      # MCP 도구 통합
│   └── parallel_processor.go # 병렬 처리 로직
├── handlers/               # HTTP 핸들러
│   └── chat_handler.go
├── go.mod                  # Go 모듈 정의
└── go.sum                  # 의존성 체크섬
```

## 🔄 병렬 처리 흐름

1. **사용자 쿼리 수신**
2. **3개의 Goroutine 동시 실행**:
   - 로컬 축구 규칙 검색
   - MCP 축구 컨텍스트 검색
   - MCP 웹 컨텍스트 검색 (필요시)
3. **결과 수집 및 통합**
4. **최적의 응답 생성**

## 🛠️ 개발 모드

개발 중에는 다음 명령으로 자동 재시작을 활용할 수 있습니다:

```bash
# Air 설치 (Go 핫 리로드 도구)
go install github.com/cosmtrek/air@latest

# 개발 모드 실행
air
```

## 🐛 문제 해결

### Go 명령을 찾을 수 없음
- Go가 올바르게 설치되었는지 확인
- PATH 환경 변수에 Go 바이너리 경로가 포함되어 있는지 확인
- 터미널을 재시작

### 포트 충돌
- 기본 포트 8000이 사용 중인 경우 환경 변수로 변경:
```bash
PORT=8080 ./football-ai-backend
```

### MCP 도구 오류
- MCP 도구가 올바르게 설치되고 구성되어 있는지 확인
- MCP 도구의 경로가 시스템 PATH에 포함되어 있는지 확인

## 📝 로그

애플리케이션은 다음과 같은 로그를 출력합니다:
- 서비스 초기화 상태
- 수신된 쿼리 정보
- 병렬 처리 결과
- 오류 및 경고 메시지

## 🔧 환경 변수

- `PORT`: 서버 포트 (기본값: 8000)
- `GIN_MODE`: Gin 프레임워크 모드 (release/debug)
- `MCP_TIMEOUT`: MCP 도구 타임아웃 (초 단위)
