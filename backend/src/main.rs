#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::process::Command;
use tauri::Manager;

fn main() {
    tauri::Builder::default()
        .setup(|_app| {
            // FastAPI 백엔드 서버 실행
            Command::new("python3")
                .arg("backend/backend/main.py")
                .spawn()
                .expect("FastAPI 실행 실패");
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("Tauri 앱 실행 실패");
}