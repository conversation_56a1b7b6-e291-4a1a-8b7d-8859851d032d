#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  use std::process::Command;
  use tauri::Manager;

  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // FastAPI 백엔드 서버 실행
      Command::new("python3")
          .arg("backend/backend/main.py")
          .spawn()
          .expect("FastAPI 실행 실패");

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
