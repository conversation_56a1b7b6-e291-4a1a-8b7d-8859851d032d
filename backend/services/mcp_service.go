package services

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"football-ai-backend/models"
)

// MCPService handles MCP (Model Context Protocol) tool interactions
type MCPService struct {
	timeout time.Duration
}

// NewMCPService creates a new MCP service
func NewMCPService() *MCPService {
	return &MCPService{
		timeout: 30 * time.Second,
	}
}

// CallTool calls an MCP tool with the given parameters
func (mcp *MCPService) CallTool(ctx context.Context, request *models.MCPToolRequest) (*models.MCPToolResponse, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, mcp.timeout)
	defer cancel()

	// Prepare the command based on the tool type
	var cmd *exec.Cmd
	
	switch request.Tool {
	case "legal-search", "football-search":
		// Example MCP tool call - adjust based on your actual MCP setup
		cmd = exec.CommandContext(ctx, "mcp", "run", "--tool", request.Tool, request.Query)
	case "web-search":
		// Example web search tool
		cmd = exec.CommandContext(ctx, "mcp", "run", "--tool", "web-search", "--query", request.Query)
	case "notion-search":
		// Example Notion search tool
		cmd = exec.CommandContext(ctx, "mcp", "run", "--tool", "notion-search", "--query", request.Query)
	default:
		return nil, fmt.Errorf("unsupported MCP tool: %s", request.Tool)
	}

	// Execute the command
	output, err := cmd.CombinedOutput()
	if err != nil {
		return &models.MCPToolResponse{
			Error: fmt.Sprintf("MCP tool execution failed: %v", err),
		}, nil
	}

	result := strings.TrimSpace(string(output))
	
	return &models.MCPToolResponse{
		Result: result,
		Data: map[string]interface{}{
			"tool":  request.Tool,
			"query": request.Query,
		},
	}, nil
}

// CallMultipleTools calls multiple MCP tools in parallel
func (mcp *MCPService) CallMultipleTools(ctx context.Context, requests []*models.MCPToolRequest) ([]*models.MCPToolResponse, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("no MCP tool requests provided")
	}

	// Create channels for results
	results := make([]*models.MCPToolResponse, len(requests))
	errors := make([]error, len(requests))
	
	// Use a channel to collect results
	type result struct {
		index    int
		response *models.MCPToolResponse
		err      error
	}
	
	resultChan := make(chan result, len(requests))

	// Launch goroutines for each tool call
	for i, request := range requests {
		go func(index int, req *models.MCPToolRequest) {
			response, err := mcp.CallTool(ctx, req)
			resultChan <- result{
				index:    index,
				response: response,
				err:      err,
			}
		}(i, request)
	}

	// Collect results
	for i := 0; i < len(requests); i++ {
		select {
		case res := <-resultChan:
			results[res.index] = res.response
			errors[res.index] = res.err
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// Check if any errors occurred
	var errorMessages []string
	for i, err := range errors {
		if err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("Tool %d (%s): %v", i, requests[i].Tool, err))
		}
	}

	if len(errorMessages) > 0 {
		return results, fmt.Errorf("some MCP tools failed: %s", strings.Join(errorMessages, "; "))
	}

	return results, nil
}

// SearchFootballContext searches for football-related context using MCP tools
func (mcp *MCPService) SearchFootballContext(ctx context.Context, query string) (*models.MCPToolResponse, error) {
	request := &models.MCPToolRequest{
		Tool:  "football-search",
		Query: query,
		Args: map[string]interface{}{
			"context": "football_rules",
			"limit":   5,
		},
	}

	return mcp.CallTool(ctx, request)
}

// SearchWebContext searches for web context using MCP tools
func (mcp *MCPService) SearchWebContext(ctx context.Context, query string) (*models.MCPToolResponse, error) {
	request := &models.MCPToolRequest{
		Tool:  "web-search",
		Query: query,
		Args: map[string]interface{}{
			"num_results": 3,
			"safe_search": true,
		},
	}

	return mcp.CallTool(ctx, request)
}

// GetAvailableTools returns a list of available MCP tools
func (mcp *MCPService) GetAvailableTools() []string {
	return []string{
		"legal-search",
		"football-search", 
		"web-search",
		"notion-search",
	}
}

// ValidateTool checks if a tool is available
func (mcp *MCPService) ValidateTool(toolName string) bool {
	availableTools := mcp.GetAvailableTools()
	for _, tool := range availableTools {
		if tool == toolName {
			return true
		}
	}
	return false
}
