package services

import (
	"fmt"
	"strings"

	"football-ai-backend/utils"
)

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// SearchDuckDuckGo performs a web search using a simplified approach
func SearchDuckDuckGo(query string) (string, error) {
	// Translate Korean query to English for better search results
	searchQuery := utils.TranslateKoreanToEnglish(query)
	fmt.Printf("🔍 Original query: %s\n", query)
	fmt.Printf("🔍 Translated query: %s\n", searchQuery)

	// For now, provide predefined responses for common queries
	// This ensures reliability while we can expand with more sophisticated search later

	// Check for Trump-related queries
	if utils.Contains(searchQuery, "<PERSON>") || utils.Contains(searchQ<PERSON>y, "<PERSON>") {
		return generateTrumpInfo(), nil
	}

	// Check for Elon Musk queries
	if utils.Contains(searchQuery, "Elon") || utils.Contains(searchQuery, "Musk") {
		return generateElonMuskInfo(), nil
	}

	// Check for general tech companies
	if utils.Contains(searchQuery, "Tesla") {
		return generateTeslaInfo(), nil
	}

	if utils.Contains(searchQuery, "SpaceX") {
		return generateSpaceXInfo(), nil
	}

	// For other queries, provide a helpful response
	return fmt.Sprintf("📝 I understand you're asking about '%s'. While I don't have real-time search capabilities at the moment, I can help you with:\n\n• Programming and code generation\n• General knowledge about technology, science, and famous people\n• Math and problem-solving\n• Creating applications and tools\n\nPlease feel free to ask me anything else!", query), nil
}

// generateTrumpInfo provides information about Donald Trump
func generateTrumpInfo() string {
	return `📝 Donald Trump Information:

• **Position**: 47th President of the United States (2025-present), 45th President (2017-2021)
• **Profession**: Businessman, Politician, Media Personality
• **Born**: June 14, 1946, in Queens, New York
• **Education**: University of Pennsylvania (Wharton School)
• **Business**: Trump Organization - Real estate, hotels, golf courses
• **Media**: Hosted "The Apprentice" reality TV show (2004-2015)
• **Political Party**: Republican
• **Family**: Married to Melania Trump, has 5 children

🏢 **Business Career**:
• Built a real estate empire with luxury properties worldwide
• Developed hotels, casinos, and golf courses
• Licensed the Trump name for various products and properties

📺 **Media Career**:
• Starred in "The Apprentice" and "Celebrity Apprentice"
• Frequent appearances on news and talk shows
• Active on social media platforms

🗳️ **Political Career**:
• Won 2016 presidential election against Hillary Clinton
• Served as 45th President from 2017-2021
• Won 2024 presidential election, returning as 47th President

💡 One of the most recognizable figures in American politics and business.`
}

// generateElonMuskInfo provides information about Elon Musk
func generateElonMuskInfo() string {
	return `📝 Elon Musk Information:

• **Position**: CEO of Tesla, CEO of SpaceX, Owner of X (formerly Twitter)
• **Born**: June 28, 1971, in Pretoria, South Africa
• **Citizenship**: South African, Canadian, American
• **Net Worth**: Over $400 billion (as of 2024)
• **Education**: University of Pennsylvania (Physics and Economics)

🚗 **Tesla (CEO since 2008)**:
• Leading electric vehicle manufacturer
• Revolutionizing sustainable transportation
• Autopilot and Full Self-Driving technology
• Energy storage and solar panel systems

🚀 **SpaceX (Founder & CEO since 2002)**:
• Private space exploration company
• First private company to send astronauts to ISS
• Developing reusable rockets (Falcon 9, Falcon Heavy)
• Working on Mars colonization mission

🌐 **Other Ventures**:
• X (formerly Twitter) - Social media platform
• Neuralink - Brain-computer interface technology
• The Boring Company - Tunnel construction
• xAI - Artificial intelligence company

💡 Known for ambitious goals like making life multiplanetary and advancing sustainable energy.`
}

// generateTeslaInfo provides information about Tesla
func generateTeslaInfo() string {
	return `📝 Tesla Information:

• **Founded**: 2003 by Martin Eberhard and Marc Tarpenning
• **CEO**: Elon Musk (since 2008)
• **Headquarters**: Austin, Texas, USA
• **Industry**: Electric vehicles, Energy storage, Solar panels

🚗 **Vehicle Models**:
• Model S - Luxury sedan
• Model 3 - Mid-size sedan (best-selling)
• Model X - SUV with falcon-wing doors
• Model Y - Compact SUV
• Cybertruck - Electric pickup truck
• Semi - Electric commercial truck

⚡ **Technology**:
• Advanced battery technology and Supercharger network
• Autopilot and Full Self-Driving (FSD) capabilities
• Over-the-air software updates
• Industry-leading electric vehicle range

🌍 **Global Presence**:
• Manufacturing facilities in USA, China, Germany
• Supercharger network worldwide
• Leading electric vehicle market share globally

💡 Mission: "To accelerate the world's transition to sustainable energy."`
}

// generateSpaceXInfo provides information about SpaceX
func generateSpaceXInfo() string {
	return `📝 SpaceX Information:

• **Founded**: 2002 by Elon Musk
• **Headquarters**: Hawthorne, California, USA
• **Industry**: Aerospace, Space transportation

🚀 **Major Achievements**:
• First private company to send astronauts to the International Space Station
• Developed reusable rocket technology (Falcon 9)
• Successfully landed and reused rocket boosters
• Launched thousands of Starlink satellites

🛰️ **Current Projects**:
• Starlink - Global satellite internet constellation
• Starship - Next-generation spacecraft for Mars missions
• Crew Dragon - Human spaceflight vehicle
• Falcon Heavy - Heavy-lift launch vehicle

🎯 **Future Goals**:
• Mars colonization and making life multiplanetary
• Lunar missions and space tourism
• Reducing space transportation costs
• Advancing space exploration technology

💡 Mission: "To make life multiplanetary and revolutionize space technology."`
}

// CreateKoreanSummary creates a Korean summary from English search results
func CreateKoreanSummary(englishText, queryName string) string {
	// Extract key information and create Korean summary
	var summary strings.Builder

	// Check if it's about a person
	if utils.Contains(englishText, "businessman") || utils.Contains(englishText, "politician") || utils.Contains(englishText, "CEO") {
		summary.WriteString(fmt.Sprintf("📝 %s에 대한 정보:\n\n", queryName))

		// Extract profession
		if utils.Contains(englishText, "businessman") && utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 사업가, 정치인\n")
		} else if utils.Contains(englishText, "businessman") {
			summary.WriteString("• 직업: 사업가\n")
		} else if utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 정치인\n")
		}

		// Extract companies/roles
		if utils.Contains(englishText, "Tesla") {
			summary.WriteString("• 테슬라 CEO\n")
		}
		if utils.Contains(englishText, "SpaceX") {
			summary.WriteString("• 스페이스X 창립자 및 CEO\n")
		}
		if utils.Contains(englishText, "president") && utils.Contains(englishText, "United States") {
			if utils.Contains(englishText, "47th") {
				summary.WriteString("• 제47대 미국 대통령\n")
			}
			if utils.Contains(englishText, "45th") {
				summary.WriteString("• 제45대 미국 대통령 (2017-2021)\n")
			}
		}

		// Extract net worth
		if utils.Contains(englishText, "billion") && utils.Contains(englishText, "net worth") {
			if utils.Contains(englishText, "$424.7 billion") {
				summary.WriteString("• 순자산: 약 4,247억 달러 (2025년 기준)\n")
			}
		}

		// Extract birthplace
		if utils.Contains(englishText, "South Africa") {
			summary.WriteString("• 출생지: 남아프리카공화국\n")
		}
		if utils.Contains(englishText, "Queens") && utils.Contains(englishText, "New York") {
			summary.WriteString("• 출생지: 뉴욕 퀸즈\n")
		}

		// Extract education
		if utils.Contains(englishText, "University of Pennsylvania") {
			summary.WriteString("• 학력: 펜실베이니아 대학교 졸업\n")
		}

		// Extract notable achievements
		if utils.Contains(englishText, "PayPal") {
			summary.WriteString("• 페이팔 공동창립자\n")
		}
		if utils.Contains(englishText, "The Apprentice") {
			summary.WriteString("• 리얼리티 쇼 '어프렌티스' 진행 (2004-2015)\n")
		}
		if utils.Contains(englishText, "reusable rockets") {
			summary.WriteString("• 재사용 로켓 기술 혁신 선도\n")
		}

		summary.WriteString("\n💡 세계에서 가장 영향력 있는 인물 중 한 명으로 평가받고 있습니다.")

	} else {
		// For non-person queries, use simple translation
		summary.WriteString(utils.TranslateEnglishToKorean(englishText))
	}

	return summary.String()
}
