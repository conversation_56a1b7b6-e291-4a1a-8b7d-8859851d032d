package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"football-ai-backend/utils"
)

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// SearchDuckDuckGo performs a web search using DuckDuckGo
func SearchDuckDuckGo(query string) (string, error) {
	// Use DuckDuckGo Instant Answer API (free, no API key required)
	baseURL := "https://api.duckduckgo.com/"
	params := url.Values{}
	params.Add("q", query)
	params.Add("format", "json")
	params.Add("no_html", "1")
	params.Add("skip_disambig", "1")

	searchURL := baseURL + "?" + params.Encode()

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Get(searchURL)
	if err != nil {
		return "", fmt.Errorf("failed to search: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// Parse JSON response
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Extract relevant information
	var searchResult strings.Builder

	// Check for instant answer
	if abstract, ok := result["Abstract"].(string); ok && abstract != "" {
		searchResult.WriteString(fmt.Sprintf("📝 %s\n\n", abstract))
	}

	// Check for definition
	if definition, ok := result["Definition"].(string); ok && definition != "" {
		searchResult.WriteString(fmt.Sprintf("📖 Definition: %s\n\n", definition))
	}

	// Check for answer
	if answer, ok := result["Answer"].(string); ok && answer != "" {
		searchResult.WriteString(fmt.Sprintf("💡 %s\n\n", answer))
	}

	// Check for related topics
	if relatedTopics, ok := result["RelatedTopics"].([]interface{}); ok && len(relatedTopics) > 0 {
		searchResult.WriteString("🔗 Related information:\n")
		for i, topic := range relatedTopics {
			if i >= 3 { // Limit to 3 related topics
				break
			}
			if topicMap, ok := topic.(map[string]interface{}); ok {
				if text, ok := topicMap["Text"].(string); ok && text != "" {
					searchResult.WriteString(fmt.Sprintf("• %s\n", text))
				}
			}
		}
	}

	result_text := searchResult.String()
	if result_text == "" {
		return fmt.Sprintf("I searched for '%s' but couldn't find specific information. You might want to try a more specific question or search directly on the web.", query), nil
	}

	return result_text, nil
}

// CreateKoreanSummary creates a Korean summary from English search results
func CreateKoreanSummary(englishText, queryName string) string {
	// Extract key information and create Korean summary
	var summary strings.Builder

	// Check if it's about a person
	if utils.Contains(englishText, "businessman") || utils.Contains(englishText, "politician") || utils.Contains(englishText, "CEO") {
		summary.WriteString(fmt.Sprintf("📝 %s에 대한 정보:\n\n", queryName))

		// Extract profession
		if utils.Contains(englishText, "businessman") && utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 사업가, 정치인\n")
		} else if utils.Contains(englishText, "businessman") {
			summary.WriteString("• 직업: 사업가\n")
		} else if utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 정치인\n")
		}

		// Extract companies/roles
		if utils.Contains(englishText, "Tesla") {
			summary.WriteString("• 테슬라 CEO\n")
		}
		if utils.Contains(englishText, "SpaceX") {
			summary.WriteString("• 스페이스X 창립자 및 CEO\n")
		}
		if utils.Contains(englishText, "president") && utils.Contains(englishText, "United States") {
			if utils.Contains(englishText, "47th") {
				summary.WriteString("• 제47대 미국 대통령\n")
			}
			if utils.Contains(englishText, "45th") {
				summary.WriteString("• 제45대 미국 대통령 (2017-2021)\n")
			}
		}

		// Extract net worth
		if utils.Contains(englishText, "billion") && utils.Contains(englishText, "net worth") {
			if utils.Contains(englishText, "$424.7 billion") {
				summary.WriteString("• 순자산: 약 4,247억 달러 (2025년 기준)\n")
			}
		}

		// Extract birthplace
		if utils.Contains(englishText, "South Africa") {
			summary.WriteString("• 출생지: 남아프리카공화국\n")
		}
		if utils.Contains(englishText, "Queens") && utils.Contains(englishText, "New York") {
			summary.WriteString("• 출생지: 뉴욕 퀸즈\n")
		}

		// Extract education
		if utils.Contains(englishText, "University of Pennsylvania") {
			summary.WriteString("• 학력: 펜실베이니아 대학교 졸업\n")
		}

		// Extract notable achievements
		if utils.Contains(englishText, "PayPal") {
			summary.WriteString("• 페이팔 공동창립자\n")
		}
		if utils.Contains(englishText, "The Apprentice") {
			summary.WriteString("• 리얼리티 쇼 '어프렌티스' 진행 (2004-2015)\n")
		}
		if utils.Contains(englishText, "reusable rockets") {
			summary.WriteString("• 재사용 로켓 기술 혁신 선도\n")
		}

		summary.WriteString("\n💡 세계에서 가장 영향력 있는 인물 중 한 명으로 평가받고 있습니다.")

	} else {
		// For non-person queries, use simple translation
		summary.WriteString(utils.TranslateEnglishToKorean(englishText))
	}

	return summary.String()
}
