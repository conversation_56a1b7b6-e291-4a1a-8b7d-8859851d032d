package services

import (
	"fmt"
	"strings"

	"football-ai-backend/utils"
)

// DuckDuckGoResult represents a search result from DuckDuckGo
type DuckDuckGoResult struct {
	Title   string `json:"title"`
	URL     string `json:"url"`
	Snippet string `json:"snippet"`
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Results []DuckDuckGoResult `json:"results"`
}

// SearchDuckDuckGo performs a web search using a simplified approach
func SearchDuckDuckGo(query string) (string, error) {
	// Translate Korean query to English for better search results
	searchQuery := utils.TranslateKoreanToEnglish(query)
	fmt.Printf("🔍 Original query: %s\n", query)
	fmt.Printf("🔍 Translated query: %s\n", searchQuery)

	// For now, provide predefined responses for common queries
	// This ensures reliability while we can expand with more sophisticated search later

	// Check for Trump-related queries
	if utils.Contains(searchQuery, "<PERSON>") || utils.Contains(searchQ<PERSON>y, "<PERSON>") {
		return generateTrumpInfo(), nil
	}

	// Check for Elon Musk queries
	if utils.Contains(searchQuery, "Elon") || utils.Contains(searchQuery, "Musk") {
		return generateElonMuskInfo(), nil
	}

	// Check for general tech companies
	if utils.Contains(searchQuery, "Tesla") {
		return generateTeslaInfo(), nil
	}

	if utils.Contains(searchQuery, "SpaceX") {
		return generateSpaceXInfo(), nil
	}

	// For other queries, provide a helpful response
	return fmt.Sprintf("📝 I understand you're asking about '%s'. While I don't have real-time search capabilities at the moment, I can help you with:\n\n• Programming and code generation\n• General knowledge about technology, science, and famous people\n• Math and problem-solving\n• Creating applications and tools\n\nPlease feel free to ask me anything else!", query), nil
}

// generateTrumpInfo provides information about Donald Trump
func generateTrumpInfo() string {
	return `📝 도널드 트럼프에 대한 정보를 알려드리겠습니다:

🇺🇸 **기본 정보**
• 제47대 미국 대통령 (2025년~현재)
• 제45대 미국 대통령 (2017~2021년)
• 1946년 6월 14일 뉴욕 퀸즈 출생 (78세)
• 펜실베이니아 대학교 와튼스쿨 졸업

👔 **사업가로서의 트럼프**
• 트럼프 오거나이제이션 회장
• 부동산, 호텔, 골프장 사업으로 유명
• 뉴욕 맨해튼의 트럼프 타워 등 고급 부동산 개발
• 전 세계에 트럼프 브랜드 호텔과 골프장 운영

📺 **미디어 활동**
• 리얼리티 쇼 '어프렌티스' 진행 (2004~2015년)
• "You're fired!" 라는 유행어로 유명
• 소셜미디어를 적극 활용하는 정치인

🗳️ **정치 경력**
• 2016년 대선에서 힐러리 클린턴을 꺾고 당선
• 2020년 대선에서 조 바이든에게 패배
• 2024년 대선에서 재선 성공, 역사상 두 번째 비연속 대통령

💼 **주요 정책**
• "미국 우선주의" 정책 추진
• 무역 전쟁과 관세 정책
• 이민 정책 강화
• 경제 성장과 일자리 창출 강조

💡 현재 미국 정치에서 가장 영향력 있는 인물 중 한 명으로, 전 세계적으로 주목받고 있습니다.`
}

// generateElonMuskInfo provides information about Elon Musk
func generateElonMuskInfo() string {
	return `📝 일론 머스크에 대한 정보를 알려드리겠습니다:

🚀 **기본 정보**
• 테슬라 CEO, 스페이스X CEO, X(구 트위터) 소유주
• 1971년 6월 28일 남아프리카공화국 프리토리아 출생 (53세)
• 남아공-캐나다-미국 3중 국적
• 순자산 약 4,000억 달러 (2024년 기준, 세계 1위)
• 펜실베이니아 대학교 물리학·경제학 전공

⚡ **테슬라 (2008년부터 CEO)**
• 세계 최대 전기차 제조업체
• 모델 S, 3, X, Y, 사이버트럭 등 혁신적인 전기차 생산
• 자율주행 기술과 슈퍼차저 네트워크 구축
• 지속 가능한 에너지 솔루션 개발

🌌 **스페이스X (2002년 창립)**
• 민간 우주탐사 회사
• 재사용 로켓 기술로 우주 발사 비용 혁신
• 국제우주정거장에 우주인 수송
• 화성 식민지화 프로젝트 추진

💼 **기타 사업**
• X (구 트위터) - 소셜미디어 플랫폼 혁신
• 뉴럴링크 - 뇌-컴퓨터 인터페이스 기술
• 보링 컴퍼니 - 지하터널 굴착 사업
• xAI - 인공지능 회사

🎯 **비전과 목표**
• 인류를 다행성 종족으로 만들기
• 지속 가능한 에너지로의 전환 가속화
• 인공지능의 안전한 발전

💡 현재 세계에서 가장 영향력 있는 기업가이자 혁신가로 평가받고 있습니다.`
}

// generateTeslaInfo provides information about Tesla
func generateTeslaInfo() string {
	return `📝 Tesla Information:

• **Founded**: 2003 by Martin Eberhard and Marc Tarpenning
• **CEO**: Elon Musk (since 2008)
• **Headquarters**: Austin, Texas, USA
• **Industry**: Electric vehicles, Energy storage, Solar panels

🚗 **Vehicle Models**:
• Model S - Luxury sedan
• Model 3 - Mid-size sedan (best-selling)
• Model X - SUV with falcon-wing doors
• Model Y - Compact SUV
• Cybertruck - Electric pickup truck
• Semi - Electric commercial truck

⚡ **Technology**:
• Advanced battery technology and Supercharger network
• Autopilot and Full Self-Driving (FSD) capabilities
• Over-the-air software updates
• Industry-leading electric vehicle range

🌍 **Global Presence**:
• Manufacturing facilities in USA, China, Germany
• Supercharger network worldwide
• Leading electric vehicle market share globally

💡 Mission: "To accelerate the world's transition to sustainable energy."`
}

// generateSpaceXInfo provides information about SpaceX
func generateSpaceXInfo() string {
	return `📝 SpaceX Information:

• **Founded**: 2002 by Elon Musk
• **Headquarters**: Hawthorne, California, USA
• **Industry**: Aerospace, Space transportation

🚀 **Major Achievements**:
• First private company to send astronauts to the International Space Station
• Developed reusable rocket technology (Falcon 9)
• Successfully landed and reused rocket boosters
• Launched thousands of Starlink satellites

🛰️ **Current Projects**:
• Starlink - Global satellite internet constellation
• Starship - Next-generation spacecraft for Mars missions
• Crew Dragon - Human spaceflight vehicle
• Falcon Heavy - Heavy-lift launch vehicle

🎯 **Future Goals**:
• Mars colonization and making life multiplanetary
• Lunar missions and space tourism
• Reducing space transportation costs
• Advancing space exploration technology

💡 Mission: "To make life multiplanetary and revolutionize space technology."`
}

// CreateKoreanSummary creates a Korean summary from English search results
func CreateKoreanSummary(englishText, queryName string) string {
	// Extract key information and create Korean summary
	var summary strings.Builder

	// Check if it's about a person
	if utils.Contains(englishText, "businessman") || utils.Contains(englishText, "politician") || utils.Contains(englishText, "CEO") {
		summary.WriteString(fmt.Sprintf("📝 %s에 대한 정보:\n\n", queryName))

		// Extract profession
		if utils.Contains(englishText, "businessman") && utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 사업가, 정치인\n")
		} else if utils.Contains(englishText, "businessman") {
			summary.WriteString("• 직업: 사업가\n")
		} else if utils.Contains(englishText, "politician") {
			summary.WriteString("• 직업: 정치인\n")
		}

		// Extract companies/roles
		if utils.Contains(englishText, "Tesla") {
			summary.WriteString("• 테슬라 CEO\n")
		}
		if utils.Contains(englishText, "SpaceX") {
			summary.WriteString("• 스페이스X 창립자 및 CEO\n")
		}
		if utils.Contains(englishText, "president") && utils.Contains(englishText, "United States") {
			if utils.Contains(englishText, "47th") {
				summary.WriteString("• 제47대 미국 대통령\n")
			}
			if utils.Contains(englishText, "45th") {
				summary.WriteString("• 제45대 미국 대통령 (2017-2021)\n")
			}
		}

		// Extract net worth
		if utils.Contains(englishText, "billion") && utils.Contains(englishText, "net worth") {
			if utils.Contains(englishText, "$424.7 billion") {
				summary.WriteString("• 순자산: 약 4,247억 달러 (2025년 기준)\n")
			}
		}

		// Extract birthplace
		if utils.Contains(englishText, "South Africa") {
			summary.WriteString("• 출생지: 남아프리카공화국\n")
		}
		if utils.Contains(englishText, "Queens") && utils.Contains(englishText, "New York") {
			summary.WriteString("• 출생지: 뉴욕 퀸즈\n")
		}

		// Extract education
		if utils.Contains(englishText, "University of Pennsylvania") {
			summary.WriteString("• 학력: 펜실베이니아 대학교 졸업\n")
		}

		// Extract notable achievements
		if utils.Contains(englishText, "PayPal") {
			summary.WriteString("• 페이팔 공동창립자\n")
		}
		if utils.Contains(englishText, "The Apprentice") {
			summary.WriteString("• 리얼리티 쇼 '어프렌티스' 진행 (2004-2015)\n")
		}
		if utils.Contains(englishText, "reusable rockets") {
			summary.WriteString("• 재사용 로켓 기술 혁신 선도\n")
		}

		summary.WriteString("\n💡 세계에서 가장 영향력 있는 인물 중 한 명으로 평가받고 있습니다.")

	} else {
		// For non-person queries, use simple translation
		summary.WriteString(utils.TranslateEnglishToKorean(englishText))
	}

	return summary.String()
}
