package services

import (
	"os"
	"testing"

	"football-ai-backend/models"
)

func TestNewFootballRulesService(t *testing.T) {
	service := NewFootballRulesService()
	if service == nil {
		t.Fatal("NewFootballRulesService returned nil")
	}
}

func TestExtractKeywords(t *testing.T) {
	tests := []struct {
		input    string
		expected []string
	}{
		{
			input:    "The Field of Play",
			expected: []string{"the", "field", "play"},
		},
		{
			input:    "Offside Rule",
			expected: []string{"offside", "rule"},
		},
		{
			input:    "a b c",
			expected: []string{}, // Words with length <= 2 are filtered out
		},
		{
			input:    "Football is a great sport",
			expected: []string{"football", "great", "sport"},
		},
	}

	for _, test := range tests {
		result := extractKeywords(test.input)
		if len(result) != len(test.expected) {
			t.Errorf("extractKeywords(%q) returned %d keywords, expected %d", 
				test.input, len(result), len(test.expected))
			continue
		}

		for i, keyword := range result {
			if keyword != test.expected[i] {
				t.<PERSON><PERSON>("extractKeywords(%q)[%d] = %q, expected %q", 
					test.input, i, keyword, test.expected[i])
			}
		}
	}
}

func TestCalculateMatchScore(t *testing.T) {
	tests := []struct {
		queryKeywords []string
		ruleKeywords  []string
		expectedScore int
	}{
		{
			queryKeywords: []string{"offside", "rule"},
			ruleKeywords:  []string{"offside", "position", "rule", "player"},
			expectedScore: 2,
		},
		{
			queryKeywords: []string{"penalty", "kick"},
			ruleKeywords:  []string{"penalty", "area", "goalkeeper"},
			expectedScore: 1,
		},
		{
			queryKeywords: []string{"corner", "kick"},
			ruleKeywords:  []string{"throw", "goal", "line"},
			expectedScore: 0,
		},
	}

	for _, test := range tests {
		score := calculateMatchScore(test.queryKeywords, test.ruleKeywords)
		if score != test.expectedScore {
			t.Errorf("calculateMatchScore(%v, %v) = %d, expected %d",
				test.queryKeywords, test.ruleKeywords, score, test.expectedScore)
		}
	}
}

func TestFindBestMatch(t *testing.T) {
	// Create a test service with mock data
	service := &FootballRulesService{
		rules: make(map[string]*models.FootballRule),
	}

	// Add test rule
	testRule := &models.FootballRule{
		Title: "Test Rule",
		Subsections: []models.FootballSubsection{
			{
				Title:    "Offside Position",
				Content:  []string{"Player is in offside position when..."},
				Keywords: []string{"offside", "position", "player"},
			},
		},
		Keywords: []string{"test", "rule", "offside", "position", "player"},
	}
	service.rules["Test Rule"] = testRule

	// Test finding a match
	match := service.FindBestMatch("What is offside position?")
	if match == nil {
		t.Fatal("FindBestMatch returned nil for offside query")
	}

	if match.Score == 0 {
		t.Error("FindBestMatch returned match with score 0")
	}

	if len(match.Content) == 0 {
		t.Error("FindBestMatch returned match with empty content")
	}

	// Test no match
	match = service.FindBestMatch("completely unrelated query xyz")
	if match != nil && match.Score > 0 {
		t.Error("FindBestMatch should not return high-score match for unrelated query")
	}
}

func TestGetDefaultResponse(t *testing.T) {
	service := NewFootballRulesService()

	tests := []struct {
		query    string
		language string
		contains string
	}{
		{
			query:    "hello",
			language: "en",
			contains: "Hello",
		},
		{
			query:    "안녕",
			language: "ko",
			contains: "안녕하세요",
		},
		{
			query:    "offside",
			language: "en",
			contains: "offside",
		},
		{
			query:    "random query",
			language: "en",
			contains: "not sure",
		},
	}

	for _, test := range tests {
		response := service.GetDefaultResponse(test.query, test.language)
		if response == "" {
			t.Errorf("GetDefaultResponse returned empty string for query: %q", test.query)
		}

		// Note: This is a simple contains check. In a real test, you might want more sophisticated checking.
		// For now, we just ensure we get a non-empty response.
	}
}

func TestLoadRulesWithInvalidFile(t *testing.T) {
	service := &FootballRulesService{
		rules: make(map[string]*models.FootballRule),
	}

	err := service.LoadRules("nonexistent_file.txt")
	if err == nil {
		t.Error("LoadRules should return error for nonexistent file")
	}
}

func TestLoadRulesWithValidContent(t *testing.T) {
	// Create a temporary test file
	content := `## 1. Test Section
### 1.1 Test Subsection
- This is test content
- Another test line

## 2. Another Section
### 2.1 Another Subsection
- More test content
`

	tmpFile, err := os.CreateTemp("", "test_rules_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(content); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tmpFile.Close()

	// Test loading the file
	service := &FootballRulesService{
		rules: make(map[string]*models.FootballRule),
	}

	err = service.LoadRules(tmpFile.Name())
	if err != nil {
		t.Fatalf("LoadRules failed: %v", err)
	}

	// Check if rules were loaded
	if len(service.rules) == 0 {
		t.Error("No rules were loaded")
	}

	// Check specific rule
	rule, exists := service.rules["1. Test Section"]
	if !exists {
		t.Error("Expected rule '1. Test Section' not found")
	}

	if len(rule.Subsections) == 0 {
		t.Error("No subsections loaded for test rule")
	}

	if len(rule.Subsections[0].Content) == 0 {
		t.Error("No content loaded for test subsection")
	}
}
