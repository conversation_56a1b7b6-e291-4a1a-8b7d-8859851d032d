package services

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"

	"football-ai-backend/models"
)

// FootballRulesService handles football rules parsing and matching
type FootballRulesService struct {
	rules map[string]*models.FootballRule
	mutex sync.RWMutex
}

// NewFootballRulesService creates a new football rules service
func NewFootballRulesService() *FootballRulesService {
	service := &FootballRulesService{
		rules: make(map[string]*models.FootballRule),
	}
	
	// Load rules on initialization
	if err := service.LoadRules("mcp_data/football.txt"); err != nil {
		fmt.Printf("Warning: Failed to load football rules: %v\n", err)
	}
	
	return service
}

// LoadRules loads and parses football rules from a file
func (frs *FootballRulesService) LoadRules(filename string) error {
	frs.mutex.Lock()
	defer frs.mutex.Unlock()

	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open rules file: %w", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var currentSection *models.FootballRule
	var currentSubsection *models.FootballSubsection
	
	// Regular expressions for parsing
	sectionRegex := regexp.MustCompile(`^## (.+)$`)
	subsectionRegex := regexp.MustCompile(`^### (.+)$`)
	contentRegex := regexp.MustCompile(`^- (.+)$`)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// Check for section header (## 1. The Field of Play)
		if matches := sectionRegex.FindStringSubmatch(line); matches != nil {
			sectionTitle := strings.TrimSpace(matches[1])
			currentSection = &models.FootballRule{
				Title:       sectionTitle,
				Subsections: []models.FootballSubsection{},
				Keywords:    extractKeywords(sectionTitle),
			}
			frs.rules[sectionTitle] = currentSection
			currentSubsection = nil
			continue
		}

		// Check for subsection header (### 1.1 Dimensions)
		if matches := subsectionRegex.FindStringSubmatch(line); matches != nil {
			if currentSection != nil {
				subsectionTitle := strings.TrimSpace(matches[1])
				newSubsection := models.FootballSubsection{
					Title:    subsectionTitle,
					Content:  []string{},
					Keywords: extractKeywords(subsectionTitle),
				}
				currentSection.Subsections = append(currentSection.Subsections, newSubsection)
				currentSubsection = &currentSection.Subsections[len(currentSection.Subsections)-1]
			}
			continue
		}

		// Check for content (- Rule content)
		if matches := contentRegex.FindStringSubmatch(line); matches != nil {
			if currentSection != nil && currentSubsection != nil {
				content := strings.TrimSpace(matches[1])
				currentSubsection.Content = append(currentSubsection.Content, content)
				
				// Add keywords from content
				contentKeywords := extractKeywords(content)
				currentSubsection.Keywords = append(currentSubsection.Keywords, contentKeywords...)
				currentSection.Keywords = append(currentSection.Keywords, contentKeywords...)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading rules file: %w", err)
	}

	return nil
}

// extractKeywords extracts keywords from text for matching
func extractKeywords(text string) []string {
	// Convert to lowercase and split by common delimiters
	text = strings.ToLower(text)
	words := regexp.MustCompile(`[^\w]+`).Split(text, -1)
	
	var keywords []string
	for _, word := range words {
		word = strings.TrimSpace(word)
		if len(word) > 2 { // Only include words longer than 2 characters
			keywords = append(keywords, word)
		}
	}
	
	return keywords
}

// FindBestMatch finds the best matching rule for a query
func (frs *FootballRulesService) FindBestMatch(query string) *models.RuleMatch {
	frs.mutex.RLock()
	defer frs.mutex.RUnlock()

	queryKeywords := extractKeywords(query)
	if len(queryKeywords) == 0 {
		return nil
	}

	var bestMatch *models.RuleMatch
	bestScore := 0

	for _, rule := range frs.rules {
		// Check section-level match
		sectionScore := calculateMatchScore(queryKeywords, rule.Keywords)
		
		// Check subsection-level matches
		for _, subsection := range rule.Subsections {
			subsectionScore := calculateMatchScore(queryKeywords, subsection.Keywords)
			
			if subsectionScore > bestScore {
				bestMatch = &models.RuleMatch{
					Title:   fmt.Sprintf("%s - %s", rule.Title, subsection.Title),
					Content: subsection.Content,
					Score:   subsectionScore,
				}
				bestScore = subsectionScore
			}
		}
		
		// If section score is better than any subsection
		if sectionScore > bestScore {
			var allContent []string
			for _, subsection := range rule.Subsections {
				allContent = append(allContent, subsection.Content...)
			}
			bestMatch = &models.RuleMatch{
				Title:   rule.Title,
				Content: allContent,
				Score:   sectionScore,
			}
			bestScore = sectionScore
		}
	}

	return bestMatch
}

// calculateMatchScore calculates the match score between query keywords and rule keywords
func calculateMatchScore(queryKeywords, ruleKeywords []string) int {
	score := 0
	querySet := make(map[string]bool)
	
	for _, keyword := range queryKeywords {
		querySet[keyword] = true
	}
	
	for _, keyword := range ruleKeywords {
		if querySet[keyword] {
			score++
		}
	}
	
	return score
}

// GetDefaultResponse returns a default response based on language and query
func (frs *FootballRulesService) GetDefaultResponse(query, language string) string {
	query = strings.ToLower(query)
	
	responses := map[string]map[string]string{
		"en": {
			"offside": "The offside rule in football states that a player is in an offside position if they are nearer to the opponent's goal line than both the ball and the second-last opponent when the ball is played to them. However, it's not an offense to be in an offside position unless the player becomes involved in active play.",
			"hello":   "Hello! How can I assist you with football rules today?",
			"hi":      "Hi there! What would you like to know about football?",
			"help":    "I can help explain football rules and answer your questions. Try asking about specific rules like 'offside', 'penalty', or 'corner kick'.",
			"thank":   "You're welcome! Feel free to ask if you have more questions about football.",
			"bye":     "Goodbye! Feel free to come back if you have more questions about football rules!",
		},
		"ko": {
			"offside": "축구의 오프사이드 규칙은 공이 전달되는 순간, 공보다 상대편 골라인에 더 가까이 있고, 공과 상대편 선수들 중 두 번째로 마지막 상대 선수보다 골라인에 더 가까이 있는 선수가 오프사이드 위치에 있게 됩니다. 다만, 오프사이드 위치에 있는 것 자체가 반칙이 아니라, 그 선수가 실제 경기에 관여할 때만 반칙이 선언됩니다.",
			"hello":   "안녕하세요! 축구 규칙에 대해 무엇이든 물어보세요!",
			"hi":      "안녕하세요! 축구에 대해 무엇이 궁금하신가요?",
			"help":    "축구 규칙에 대해 도와드릴게요. '오프사이드', '페널티킥', '코너킥' 등에 대해 물어보세요.",
			"thank":   "천만에요! 축구 규칙에 대해 더 궁금한 점이 있으시면 언제든지 물어보세요.",
			"bye":     "안녕히 가세요! 축구 규칙에 대해 더 궁금한 점이 있으시면 언제든지 다시 오세요!",
		},
	}

	langResponses, exists := responses[language]
	if !exists {
		langResponses = responses["en"]
	}

	// Check for keywords in the query
	for keyword, response := range langResponses {
		if strings.Contains(query, keyword) {
			return response
		}
	}

	// Default response if no keywords match
	defaultResponses := map[string]string{
		"en": "I'm not sure I understand. Could you ask about a specific football rule? For example, you could ask 'What is offside?' or 'Explain the penalty kick rule.'",
		"ko": "이해하지 못했어요. 특정 축구 규칙에 대해 물어보시겠어요? 예를 들어 '오프사이드가 뭐예요?' 또는 '페널티킥 규칙을 알려주세요'라고 물어보실 수 있어요.",
	}

	if response, exists := defaultResponses[language]; exists {
		return response
	}
	return defaultResponses["en"]
}
