package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"football-ai-backend/models"
)

// ParallelProcessor handles parallel processing of multiple data sources
type ParallelProcessor struct {
	footballService *FootballRulesService
	mcpService      *MCPService
	timeout         time.Duration
}

// NewParallelProcessor creates a new parallel processor
func NewParallelProcessor(footballService *FootballRulesService, mcpService *MCPService) *ParallelProcessor {
	return &ParallelProcessor{
		footballService: footballService,
		mcpService:      mcpService,
		timeout:         15 * time.Second,
	}
}

// ProcessQuery processes a query using multiple sources in parallel
func (pp *ParallelProcessor) ProcessQuery(ctx context.Context, query, language string) (*models.ChatResponse, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(ctx, pp.timeout)
	defer cancel()

	// Channel to collect results
	resultChan := make(chan *models.ProcessingResult, 3)
	var wg sync.WaitGroup

	// 1. Search football rules (local)
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := &models.ProcessingResult{Source: "football_rules"}
		
		match := pp.footballService.FindBestMatch(query)
		if match != nil && match.Score > 0 {
			result.RuleMatch = match
		}
		
		select {
		case resultChan <- result:
		case <-ctx.Done():
		}
	}()

	// 2. Search using MCP football context
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := &models.ProcessingResult{Source: "mcp_football"}
		
		mcpResponse, err := pp.mcpService.SearchFootballContext(ctx, query)
		if err != nil {
			result.Error = err
		} else {
			result.MCPResponse = mcpResponse
		}
		
		select {
		case resultChan <- result:
		case <-ctx.Done():
		}
	}()

	// 3. Search using MCP web context (optional, for additional context)
	wg.Add(1)
	go func() {
		defer wg.Done()
		result := &models.ProcessingResult{Source: "mcp_web"}
		
		// Only search web if query seems to need external context
		if pp.shouldSearchWeb(query) {
			mcpResponse, err := pp.mcpService.SearchWebContext(ctx, query)
			if err != nil {
				result.Error = err
			} else {
				result.MCPResponse = mcpResponse
			}
		}
		
		select {
		case resultChan <- result:
		case <-ctx.Done():
		}
	}()

	// Close the channel when all goroutines are done
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	var results []*models.ProcessingResult
	for result := range resultChan {
		results = append(results, result)
	}

	// Generate response based on collected results
	return pp.generateResponse(results, query, language), nil
}

// shouldSearchWeb determines if web search is needed for the query
func (pp *ParallelProcessor) shouldSearchWeb(query string) bool {
	// Simple heuristic: search web for recent events, statistics, or specific player/team queries
	webKeywords := []string{
		"recent", "latest", "news", "today", "yesterday", "2024", "2023",
		"statistics", "stats", "player", "team", "match", "game",
		"world cup", "champions league", "premier league",
	}
	
	queryLower := query
	for _, keyword := range webKeywords {
		if contains(queryLower, keyword) {
			return true
		}
	}
	
	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr || 
		      containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// generateResponse generates a final response based on all collected results
func (pp *ParallelProcessor) generateResponse(results []*models.ProcessingResult, query, language string) *models.ChatResponse {
	var bestRuleMatch *models.RuleMatch
	var mcpResponses []*models.MCPToolResponse
	var errors []error

	// Process results
	for _, result := range results {
		if result.Error != nil {
			errors = append(errors, result.Error)
			continue
		}

		if result.RuleMatch != nil {
			if bestRuleMatch == nil || result.RuleMatch.Score > bestRuleMatch.Score {
				bestRuleMatch = result.RuleMatch
			}
		}

		if result.MCPResponse != nil && result.MCPResponse.Result != "" {
			mcpResponses = append(mcpResponses, result.MCPResponse)
		}
	}

	// Generate response text
	var responseText string

	// Priority 1: Use best rule match if found
	if bestRuleMatch != nil && bestRuleMatch.Score > 0 {
		responseText = fmt.Sprintf("%s:\n\n", bestRuleMatch.Title)
		for _, content := range bestRuleMatch.Content {
			responseText += fmt.Sprintf("- %s\n", content)
		}

		// Add MCP context if available
		if len(mcpResponses) > 0 {
			responseText += "\n\n📋 Additional Context:\n"
			for i, mcpResp := range mcpResponses {
				if mcpResp.Result != "" {
					responseText += fmt.Sprintf("\n%d. %s", i+1, mcpResp.Result)
				}
			}
		}
	} else if len(mcpResponses) > 0 {
		// Priority 2: Use MCP responses if no rule match
		responseText = "Based on available information:\n\n"
		for i, mcpResp := range mcpResponses {
			if mcpResp.Result != "" {
				responseText += fmt.Sprintf("%d. %s\n\n", i+1, mcpResp.Result)
			}
		}
	} else {
		// Priority 3: Use default response
		responseText = pp.footballService.GetDefaultResponse(query, language)
	}

	// Add error information if needed (for debugging)
	if len(errors) > 0 && responseText == pp.footballService.GetDefaultResponse(query, language) {
		// Only add error info if we're falling back to default response
		fmt.Printf("Processing errors occurred: %v\n", errors)
	}

	return &models.ChatResponse{
		Response: responseText,
		Status:   "success",
		Language: language,
	}
}

// ProcessQuerySimple processes a query using only local football rules (fallback)
func (pp *ParallelProcessor) ProcessQuerySimple(query, language string) *models.ChatResponse {
	match := pp.footballService.FindBestMatch(query)
	
	var responseText string
	if match != nil && match.Score > 0 {
		responseText = fmt.Sprintf("%s:\n\n", match.Title)
		for _, content := range match.Content {
			responseText += fmt.Sprintf("- %s\n", content)
		}
	} else {
		responseText = pp.footballService.GetDefaultResponse(query, language)
	}

	return &models.ChatResponse{
		Response: responseText,
		Status:   "success",
		Language: language,
	}
}
