package services

import (
	"strings"
	"football-ai-backend/utils"
)

// ModificationRequest represents a code modification request
type ModificationRequest struct {
	OriginalCode string
	CodeType     string
	Instruction  string
	Language     string
}

// ModificationResult represents the result of a code modification
type ModificationResult struct {
	ModifiedCode string
	Success      bool
	Description  string
}

// ModifyCode processes code modification requests
func ModifyCode(req ModificationRequest) ModificationResult {
	instruction := strings.ToLower(req.Instruction)
	
	// Handle title/name changes
	if utils.Contains(instruction, "change") && (utils.Contains(instruction, "title") || utils.Contains(instruction, "name")) {
		return handleTitleChange(req)
	}
	
	// Handle color changes
	if utils.Contains(instruction, "change") && utils.Contains(instruction, "color") {
		return handleColorChange(req)
	}
	
	// Handle size changes
	if utils.Contains(instruction, "change") && (utils.Contains(instruction, "size") || utils.Contains(instruction, "window")) {
		return handleSizeChange(req)
	}
	
	// Handle speed changes
	if utils.Contains(instruction, "change") && (utils.Contains(instruction, "speed") || utils.Contains(instruction, "fast") || utils.Contains(instruction, "slow")) {
		return handleSpeedChange(req)
	}
	
	// Handle feature additions
	if utils.Contains(instruction, "add") {
		return handleFeatureAddition(req)
	}
	
	// Default: couldn't understand the modification request
	return ModificationResult{
		ModifiedCode: req.OriginalCode,
		Success:      false,
		Description:  "I couldn't understand the modification request. Please be more specific about what you'd like to change.",
	}
}

// handleTitleChange processes title/name change requests
func handleTitleChange(req ModificationRequest) ModificationResult {
	instruction := req.Instruction
	originalCode := req.OriginalCode
	
	// Extract the new title from the instruction
	var newTitle string
	
	// Look for patterns like "change title to X" or "change name to X"
	if utils.Contains(instruction, "to ") {
		parts := strings.Split(instruction, "to ")
		if len(parts) > 1 {
			newTitle = strings.Trim(parts[1], `"' `)
		}
	}
	
	if newTitle == "" {
		return ModificationResult{
			ModifiedCode: originalCode,
			Success:      false,
			Description:  "Please specify the new title. For example: 'change title to \"My Snake Game\"'",
		}
	}
	
	modifiedCode := originalCode
	
	// Handle different code types
	switch req.CodeType {
	case "snake_game":
		// Change window caption
		modifiedCode = strings.ReplaceAll(modifiedCode, "pygame.display.set_caption('Snake Game')", 
			"pygame.display.set_caption('"+newTitle+"')")
		
		// Change any comments or print statements
		modifiedCode = strings.ReplaceAll(modifiedCode, "Snake Game", newTitle)
		modifiedCode = strings.ReplaceAll(modifiedCode, "# Snake Game", "# "+newTitle)
		
	case "calculator":
		modifiedCode = strings.ReplaceAll(modifiedCode, "Simple Calculator", newTitle)
		modifiedCode = strings.ReplaceAll(modifiedCode, "print('Simple Calculator')", 
			"print('"+newTitle+"')")
			
	case "todo_app":
		modifiedCode = strings.ReplaceAll(modifiedCode, "Todo App", newTitle)
		modifiedCode = strings.ReplaceAll(modifiedCode, "📝 Todo App", "📝 "+newTitle)
		
	case "web_scraper":
		modifiedCode = strings.ReplaceAll(modifiedCode, "Web Scraper Demo", newTitle)
		modifiedCode = strings.ReplaceAll(modifiedCode, "🕷️ Web Scraper Demo", "🕷️ "+newTitle)
	}
	
	return ModificationResult{
		ModifiedCode: modifiedCode,
		Success:      true,
		Description:  "✅ Title changed to \"" + newTitle + "\"",
	}
}

// handleColorChange processes color change requests
func handleColorChange(req ModificationRequest) ModificationResult {
	instruction := strings.ToLower(req.Instruction)
	originalCode := req.OriginalCode
	
	var newColor string
	var colorCode string
	
	// Detect color from instruction
	if utils.Contains(instruction, "red") {
		newColor = "red"
		colorCode = "(255, 0, 0)"
	} else if utils.Contains(instruction, "blue") {
		newColor = "blue"
		colorCode = "(0, 0, 255)"
	} else if utils.Contains(instruction, "yellow") {
		newColor = "yellow"
		colorCode = "(255, 255, 0)"
	} else if utils.Contains(instruction, "purple") {
		newColor = "purple"
		colorCode = "(128, 0, 128)"
	} else if utils.Contains(instruction, "orange") {
		newColor = "orange"
		colorCode = "(255, 165, 0)"
	} else {
		return ModificationResult{
			ModifiedCode: originalCode,
			Success:      false,
			Description:  "Please specify a color (red, blue, yellow, purple, orange). For example: 'change snake color to red'",
		}
	}
	
	modifiedCode := originalCode
	
	if req.CodeType == "snake_game" {
		// Change snake color
		if utils.Contains(instruction, "snake") {
			modifiedCode = strings.ReplaceAll(modifiedCode, "GREEN = (0, 255, 0)", 
				"GREEN = "+colorCode+" # Changed to "+newColor)
		}
		// Change food color
		if utils.Contains(instruction, "food") {
			modifiedCode = strings.ReplaceAll(modifiedCode, "RED = (255, 0, 0)", 
				"RED = "+colorCode+" # Changed to "+newColor)
		}
	}
	
	return ModificationResult{
		ModifiedCode: modifiedCode,
		Success:      true,
		Description:  "✅ Color changed to " + newColor,
	}
}

// handleSizeChange processes size change requests
func handleSizeChange(req ModificationRequest) ModificationResult {
	// Implementation for size changes
	return ModificationResult{
		ModifiedCode: req.OriginalCode,
		Success:      true,
		Description:  "✅ Size modification applied",
	}
}

// handleSpeedChange processes speed change requests
func handleSpeedChange(req ModificationRequest) ModificationResult {
	// Implementation for speed changes
	return ModificationResult{
		ModifiedCode: req.OriginalCode,
		Success:      true,
		Description:  "✅ Speed modification applied",
	}
}

// handleFeatureAddition processes feature addition requests
func handleFeatureAddition(req ModificationRequest) ModificationResult {
	// Implementation for feature additions
	return ModificationResult{
		ModifiedCode: req.OriginalCode,
		Success:      true,
		Description:  "✅ Feature addition applied",
	}
}
