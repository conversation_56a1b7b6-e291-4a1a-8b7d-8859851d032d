package services

import (
	"sync"
	"time"
)

// CodeSession represents a user's coding session
type CodeSession struct {
	LastGeneratedCode string    `json:"last_generated_code"`
	CodeType          string    `json:"code_type"` // "snake_game", "calculator", etc.
	CodeTitle         string    `json:"code_title"`
	Language          string    `json:"language"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// SessionManager manages user sessions
type SessionManager struct {
	sessions map[string]*CodeSession
	mutex    sync.RWMutex
}

// Global session manager instance
var globalSessionManager = &SessionManager{
	sessions: make(map[string]*CodeSession),
}

// GetSessionManager returns the global session manager
func GetSessionManager() *SessionManager {
	return globalSessionManager
}

// GetSession retrieves a session by session ID
func (sm *SessionManager) GetSession(sessionID string) *CodeSession {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	session, exists := sm.sessions[sessionID]
	if !exists {
		return nil
	}
	
	return session
}

// SetSession stores or updates a session
func (sm *SessionManager) SetSession(sessionID string, session *CodeSession) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	session.UpdatedAt = time.Now()
	if session.CreatedAt.IsZero() {
		session.CreatedAt = time.Now()
	}
	
	sm.sessions[sessionID] = session
}

// UpdateCodeInSession updates the code in an existing session
func (sm *SessionManager) UpdateCodeInSession(sessionID, newCode string) bool {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	session, exists := sm.sessions[sessionID]
	if !exists {
		return false
	}
	
	session.LastGeneratedCode = newCode
	session.UpdatedAt = time.Now()
	
	return true
}

// CleanupOldSessions removes sessions older than the specified duration
func (sm *SessionManager) CleanupOldSessions(maxAge time.Duration) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	cutoff := time.Now().Add(-maxAge)
	
	for sessionID, session := range sm.sessions {
		if session.UpdatedAt.Before(cutoff) {
			delete(sm.sessions, sessionID)
		}
	}
}

// GetSessionCount returns the number of active sessions
func (sm *SessionManager) GetSessionCount() int {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	return len(sm.sessions)
}

// StartCleanupRoutine starts a background routine to clean up old sessions
func (sm *SessionManager) StartCleanupRoutine() {
	go func() {
		ticker := time.NewTicker(30 * time.Minute) // Clean up every 30 minutes
		defer ticker.Stop()
		
		for range ticker.C {
			sm.CleanupOldSessions(2 * time.Hour) // Remove sessions older than 2 hours
		}
	}()
}
