package utils

import "strings"

// Contains checks if a string contains a substring (case-insensitive)
func Contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr)
}

// findSubstring finds a substring in a string
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Min returns the minimum of two integers
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// TranslateKoreanToEnglish translates common Korean names and terms to English for better search results
func TranslateKoreanToEnglish(query string) string {
	translations := map[string]string{
		"엘론 머스크":  "Elon Musk",
		"일론 머스크":  "Elon Musk",
		"도널드 트럼프": "Donald Trump",
		"트럼프":     "Trump",
		"빌 게이츠":   "<PERSON> Gates",
		"스티브 잡스":  "<PERSON>",
		"마크 저커버그": "<PERSON>",
		"제프 베조스":  "<PERSON>",
		"워렌 버핏":   "<PERSON> Buffett",
		"테슬라":     "Tesla",
		"스페이스X":   "SpaceX",
		"애플":      "Apple",
		"구글":      "Google",
		"마이크로소프트": "Microsoft",
		"아마존":     "Amazon",
		"페이스북":    "Facebook",
		"메타":      "Meta",
		"넷플릭스":    "Netflix",
		"삼성":      "Samsung",
		"LG":      "LG",
		"현대":      "Hyundai",
		"기아":      "Kia",
	}

	// First, translate Korean names to English
	translatedQuery := query
	for korean, english := range translations {
		if Contains(translatedQuery, korean) {
			// Replace the entire Korean name with English name
			translatedQuery = strings.ReplaceAll(translatedQuery, korean, english)
		}
	}

	// Additional cleanup for partial matches
	if Contains(translatedQuery, "도널드") && Contains(translatedQuery, "Trump") {
		translatedQuery = strings.ReplaceAll(translatedQuery, "도널드", "Donald")
	}

	// Remove Korean question patterns and keep only the essential part
	questionPatterns := []string{
		"가 누구야?", "가 누구인가요?", "가 누구지?", "가 누구예요?",
		"는 누구야?", "는 누구인가요?", "는 누구지?", "는 누구예요?",
		"이 누구야?", "이 누구인가요?", "이 누구지?", "이 누구예요?",
		"에 대해 알려줘", "에 대해서 알려줘", "에 대해 설명해줘",
		"가 뭐야?", "가 무엇인가요?", "는 뭐야?", "는 무엇인가요?",
		"이 뭐야?", "이 무엇인가요?",
	}

	for _, pattern := range questionPatterns {
		if strings.Contains(translatedQuery, pattern) {
			translatedQuery = strings.ReplaceAll(translatedQuery, pattern, "")
			break
		}
	}

	// Clean up extra spaces
	translatedQuery = strings.TrimSpace(translatedQuery)

	// If the query is empty after cleaning, return the original
	if translatedQuery == "" {
		return query
	}

	return translatedQuery
}

// TranslateEnglishToKorean translates common English terms to Korean for better user experience
func TranslateEnglishToKorean(text string) string {
	translations := map[string]string{
		"businessman":        "사업가",
		"politician":         "정치인",
		"president":          "대통령",
		"CEO":                "최고경영자",
		"founder":            "창립자",
		"co-founder":         "공동창립자",
		"entrepreneur":       "기업가",
		"billionaire":        "억만장자",
		"company":            "회사",
		"corporation":        "기업",
		"technology":         "기술",
		"innovation":         "혁신",
		"electric":           "전기",
		"vehicle":            "차량",
		"space":              "우주",
		"rocket":             "로켓",
		"university":         "대학교",
		"graduated":          "졸업했습니다",
		"bachelor":           "학사",
		"degree":             "학위",
		"economics":          "경제학",
		"real estate":        "부동산",
		"business":           "사업",
		"hotel":              "호텔",
		"casino":             "카지노",
		"golf course":        "골프장",
		"television":         "텔레비전",
		"show":               "쇼",
		"reality":            "리얼리티",
		"election":           "선거",
		"Republican":         "공화당",
		"Democratic":         "민주당",
		"Party":              "당",
		"Tesla":              "테슬라",
		"SpaceX":             "스페이스X",
		"PayPal":             "페이팔",
		"Twitter":            "트위터",
		"X":                  "X",
		"born":               "태어났습니다",
		"family":             "가족",
		"wealthy":            "부유한",
		"South Africa":       "남아프리카",
		"Canada":             "캐나다",
		"United States":      "미국",
		"California":         "캘리포니아",
		"Pennsylvania":       "펜실베이니아",
		"Queens":             "퀸즈",
		"New York":           "뉴욕",
		"net worth":          "순자산",
		"billion":            "억",
		"million":            "백만",
		"dollars":            "달러",
		"USD":                "미국 달러",
		"Forbes":             "포브스",
		"estimates":          "추정합니다",
		"leadership":         "리더십",
		"chief engineer":     "최고기술책임자",
		"reusable":           "재사용 가능한",
		"commercial":         "상업적",
		"spaceflight":        "우주비행",
		"innovations":        "혁신들",
		"acquired":           "인수했습니다",
		"merged":             "합병했습니다",
		"citizen":            "시민",
		"American":           "미국인",
		"emigrated":          "이민했습니다",
		"ventures":           "벤처",
		"pursue":             "추구하기 위해",
		"software":           "소프트웨어",
		"online":             "온라인",
		"payment":            "결제",
		"eBay":               "이베이",
		"media":              "미디어",
		"personality":        "인물",
		"member":             "구성원",
		"served":             "재직했습니다",
		"skyscrapers":        "고층빌딩",
		"licensing":          "라이선싱",
		"bankruptcies":       "파산",
		"hosted":             "진행했습니다",
		"bolstering":         "강화하며",
		"fame":               "명성",
		"presenting":         "자신을 내세우며",
		"outsider":           "아웃사이더",
		"nominee":            "후보",
		"Hillary Clinton":    "힐러리 클린턴",
		"The Apprentice":     "어프렌티스",
		"Trump Organization": "트럼프 오거나이제이션",
	}

	result := text
	for english, korean := range translations {
		// Case-insensitive replacement
		result = strings.ReplaceAll(result, english, korean)
		result = strings.ReplaceAll(result, strings.ToLower(english), korean)
		result = strings.ReplaceAll(result, strings.ToUpper(english), korean)
		result = strings.ReplaceAll(result, strings.Title(english), korean)
	}

	return result
}
