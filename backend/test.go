package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("🧪 Testing Go environment...")
	
	// Test file access
	if _, err := os.Stat("../mcp_data/football.txt"); err != nil {
		fmt.Printf("❌ Cannot access football.txt: %v\n", err)
	} else {
		fmt.Println("✅ football.txt file accessible")
	}
	
	// Test current directory
	pwd, _ := os.Getwd()
	fmt.Printf("📁 Current directory: %s\n", pwd)
	
	fmt.Println("✅ Go environment test completed")
}
