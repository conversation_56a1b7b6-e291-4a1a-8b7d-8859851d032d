#!/bin/bash

# Test script for Football AI Go Backend
# This script tests various endpoints of the Go backend

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:8000"

echo -e "${GREEN}🧪 Testing Football AI Go Backend${NC}"
echo -e "${YELLOW}Base URL: $BASE_URL${NC}"
echo ""

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}Testing: $description${NC}"
    echo -e "  ${method} ${BASE_URL}${endpoint}"
    
    if [ -n "$data" ]; then
        echo -e "  Data: $data"
        response=$(curl -s -X $method "$BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data" \
            -w "\nHTTP_CODE:%{http_code}")
    else
        response=$(curl -s -X $method "$BASE_URL$endpoint" \
            -w "\nHTTP_CODE:%{http_code}")
    fi
    
    # Extract HTTP code and response body
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "  ${GREEN}✓ Success (HTTP $http_code)${NC}"
        echo -e "  Response: $response_body" | head -c 200
        if [ ${#response_body} -gt 200 ]; then
            echo "..."
        fi
    else
        echo -e "  ${RED}✗ Failed (HTTP $http_code)${NC}"
        echo -e "  Response: $response_body"
    fi
    echo ""
}

# Test 1: Root endpoint
test_endpoint "GET" "/" "" "Root endpoint"

# Test 2: Health check
test_endpoint "GET" "/api/health" "" "Health check"

# Test 3: Simple English query
test_endpoint "POST" "/ask" '{
    "query": "What is offside?",
    "language": "en"
}' "Simple English query about offside"

# Test 4: Korean query
test_endpoint "POST" "/ask" '{
    "query": "오프사이드가 뭐예요?",
    "language": "ko"
}' "Korean query about offside"

# Test 5: Complex query
test_endpoint "POST" "/ask" '{
    "query": "What are the dimensions of a football field?",
    "language": "en",
    "messages": [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi! How can I help you with football rules?"}
    ]
}' "Complex query with message history"

# Test 6: Available MCP tools
test_endpoint "GET" "/mcp/tools" "" "List available MCP tools"

# Test 7: Invalid request
test_endpoint "POST" "/ask" '{
    "invalid": "request"
}' "Invalid request (should fail)"

# Test 8: OPTIONS request (CORS preflight)
test_endpoint "OPTIONS" "/ask" "" "CORS preflight request"

echo -e "${GREEN}🏁 Testing completed!${NC}"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo -e "  - Make sure the Go backend is running on port 8000"
echo -e "  - Check the backend logs for detailed processing information"
echo -e "  - Use 'go run main.go' to start the backend if not running"
