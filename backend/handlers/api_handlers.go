package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"football-ai-backend/generators"
	"football-ai-backend/services"
	"football-ai-backend/utils"

	"github.com/gin-gonic/gin"
)

// SimpleRequest represents the request structure
type SimpleRequest struct {
	Query     string `json:"query"`
	Language  string `json:"language"`
	SessionID string `json:"session_id,omitempty"`
}

// SimpleResponse represents the response structure
type SimpleResponse struct {
	Response     string `json:"response"`
	Status       string `json:"status"`
	Language     string `json:"language"`
	CodeContent  string `json:"code_content,omitempty"`
	Instructions string `json:"instructions,omitempty"`
	IsCode       bool   `json:"is_code"`
}

// HandleChatRequest handles the main chat API endpoint
func HandleChatRequest(c *gin.Context) {
	var req SimpleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		fmt.Printf("❌ Invalid request: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	fmt.Printf("📝 Query: %s, Language: %s, Session: %s\n", req.Query, req.Language, req.SessionID)

	// Get or create session
	sessionManager := services.GetSessionManager()
	sessionID := req.SessionID
	if sessionID == "" {
		sessionID = fmt.Sprintf("session_%d", time.Now().UnixNano())
	}

	// Enhanced response logic for general AI assistant
	var responseText string
	query := strings.ToLower(req.Query)

	// Check if this is a code modification request
	session := sessionManager.GetSession(sessionID)
	if session != nil && session.LastGeneratedCode != "" {
		// Check for modification keywords
		if utils.Contains(query, "change") || utils.Contains(query, "modify") || utils.Contains(query, "update") {
			modReq := services.ModificationRequest{
				OriginalCode: session.LastGeneratedCode,
				CodeType:     session.CodeType,
				Instruction:  req.Query,
				Language:     req.Language,
			}

			result := services.ModifyCode(modReq)
			if result.Success {
				// Update session with modified code
				sessionManager.UpdateCodeInSession(sessionID, result.ModifiedCode)

				response := SimpleResponse{
					Response:     result.ModifiedCode,
					Status:       "success",
					Language:     req.Language,
					CodeContent:  result.ModifiedCode,
					Instructions: result.Description,
					IsCode:       true,
				}
				c.JSON(http.StatusOK, response)
				return
			} else {
				responseText = result.Description
			}
		}
	}

	// Check for specific code requests first
	if (utils.Contains(query, "snake game") || (utils.Contains(query, "snake") && utils.Contains(query, "game"))) && utils.Contains(query, "python") {
		codeContent := generators.GenerateSnakeGameCode()

		// Save to session
		sessionManager.SetSession(sessionID, &services.CodeSession{
			LastGeneratedCode: codeContent,
			CodeType:          "snake_game",
			CodeTitle:         "Snake Game",
			Language:          req.Language,
		})

		response := SimpleResponse{
			Response:     codeContent,
			Status:       "success",
			Language:     req.Language,
			CodeContent:  codeContent,
			Instructions: generators.GenerateSnakeGameInstructions(),
			IsCode:       true,
		}
		c.JSON(http.StatusOK, response)
		return
	} else if utils.Contains(query, "calculator") && utils.Contains(query, "python") {
		codeContent := generators.GenerateCalculatorCode()

		// Save to session
		sessionManager.SetSession(sessionID, &services.CodeSession{
			LastGeneratedCode: codeContent,
			CodeType:          "calculator",
			CodeTitle:         "Calculator",
			Language:          req.Language,
		})

		response := SimpleResponse{
			Response:     codeContent,
			Status:       "success",
			Language:     req.Language,
			CodeContent:  codeContent,
			Instructions: generators.GenerateCalculatorInstructions(),
			IsCode:       true,
		}
		c.JSON(http.StatusOK, response)
		return
	} else if utils.Contains(query, "todo") && utils.Contains(query, "python") {
		codeContent := generators.GenerateTodoAppCode()
		response := SimpleResponse{
			Response:     codeContent,
			Status:       "success",
			Language:     req.Language,
			CodeContent:  codeContent,
			Instructions: generators.GenerateTodoAppInstructions(),
			IsCode:       true,
		}
		c.JSON(http.StatusOK, response)
		return
	} else if utils.Contains(query, "web scraper") && utils.Contains(query, "python") {
		codeContent := generators.GenerateWebScraperCode()
		response := SimpleResponse{
			Response:     codeContent,
			Status:       "success",
			Language:     req.Language,
			CodeContent:  codeContent,
			Instructions: generators.GenerateWebScraperInstructions(),
			IsCode:       true,
		}
		c.JSON(http.StatusOK, response)
		return
	}

	// Other responses
	if utils.Contains(query, "hello") || utils.Contains(query, "hi") || utils.Contains(query, "안녕") {
		responseText = "Hello! I'm your AI assistant. I can help with programming, answer questions, and generate code. What would you like to know?"
	} else if utils.Contains(query, "help") || utils.Contains(query, "도움") {
		responseText = "I can help you with:\n• Programming and code generation\n• General questions and information\n• Math and science topics\n• Technology discussions\n\nJust ask me anything!"
	} else {
		// Try web search for unknown queries
		searchResult, err := services.SearchDuckDuckGo(req.Query)
		if err != nil {
			responseText = "I'm sorry, I couldn't find specific information about that. Could you please rephrase your question or be more specific?"
		} else {
			responseText = searchResult
		}
	}

	response := SimpleResponse{
		Response: responseText,
		Status:   "success",
		Language: req.Language,
	}

	fmt.Printf("📤 Sending response: %s\n", responseText[:utils.Min(50, len(responseText))])
	c.JSON(http.StatusOK, response)
}

// HandleHealthCheck handles the health check endpoint
func HandleHealthCheck(c *gin.Context) {
	fmt.Println("📥 Health check request")
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
