package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"football-ai-backend/models"
	"football-ai-backend/services"
)

// ChatHandler handles chat-related HTTP requests
type Chat<PERSON>and<PERSON> struct {
	processor *services.ParallelProcessor
}

// NewChatHandler creates a new chat handler
func NewChatHandler(processor *services.ParallelProcessor) *ChatHandler {
	return &ChatHandler{
		processor: processor,
	}
}

// HandleAsk handles the /ask endpoint
func (ch *<PERSON>t<PERSON>and<PERSON>) HandleAsk(c *gin.Context) {
	var request models.ChatRequest
	
	// Bind JSON request
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   err.Error(),
			Status:  "error",
			Message: "Invalid request format",
		})
		return
	}

	// Validate request
	if request.Query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "query is required",
			Status:  "error", 
			Message: "Query parameter cannot be empty",
		})
		return
	}

	// Set default language if not provided
	if request.Language == "" {
		request.Language = "en"
	}

	// Log the request
	fmt.Printf("Processing query: %s (language: %s)\n", request.Query, request.Language)
	fmt.Printf("Previous messages: %d\n", len(request.Messages))

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Process the query using parallel processing
	response, err := ch.processor.ProcessQuery(ctx, request.Query, request.Language)
	if err != nil {
		fmt.Printf("Error processing query: %v\n", err)
		
		// Fallback to simple processing
		response = ch.processor.ProcessQuerySimple(request.Query, request.Language)
		response.Status = "partial_success" // Indicate that some processing failed
	}

	// Return the response
	c.JSON(http.StatusOK, response)
}

// HandleAskOptions handles OPTIONS requests for /ask endpoint (CORS preflight)
func (ch *ChatHandler) HandleAskOptions(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// HandleHealth handles the /api/health endpoint
func (ch *ChatHandler) HandleHealth(c *gin.Context) {
	response := models.HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Message:   "Football AI Backend is running!",
	}
	c.JSON(http.StatusOK, response)
}

// HandleRoot handles the root endpoint
func (ch *ChatHandler) HandleRoot(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message":   "Football AI Backend is running!",
		"version":   "2.0.0",
		"language":  "Go",
		"features": []string{
			"parallel_processing",
			"mcp_tools_integration", 
			"football_rules_matching",
			"multi_language_support",
		},
	})
}

// HandleMCPTools handles requests to test MCP tools
func (ch *ChatHandler) HandleMCPTools(c *gin.Context) {
	var request struct {
		Tool  string `json:"tool" binding:"required"`
		Query string `json:"query" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   err.Error(),
			Status:  "error",
			Message: "Invalid request format",
		})
		return
	}

	// Create MCP service for testing
	mcpService := services.NewMCPService()
	
	// Validate tool
	if !mcpService.ValidateTool(request.Tool) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   fmt.Sprintf("unsupported tool: %s", request.Tool),
			Status:  "error",
			Message: "The requested MCP tool is not available",
		})
		return
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Call the MCP tool
	mcpRequest := &models.MCPToolRequest{
		Tool:  request.Tool,
		Query: request.Query,
	}

	response, err := mcpService.CallTool(ctx, mcpRequest)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   err.Error(),
			Status:  "error",
			Message: "Failed to call MCP tool",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":   "success",
		"tool":     request.Tool,
		"query":    request.Query,
		"response": response,
	})
}

// HandleAvailableTools returns the list of available MCP tools
func (ch *ChatHandler) HandleAvailableTools(c *gin.Context) {
	mcpService := services.NewMCPService()
	tools := mcpService.GetAvailableTools()
	
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"tools":  tools,
		"count":  len(tools),
	})
}
