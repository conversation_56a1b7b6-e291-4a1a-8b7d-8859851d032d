from fastapi import Fast<PERSON><PERSON>, Request, Response, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import subprocess
import uvicorn
import json
import requests
from typing import Dict, Any, Optional

# Configuration
MODEL_NAME = "qwen3:0.6b"
OLLAMA_API_URL = "http://localhost:11434/api/generate"
TIMEOUT = 30  # seconds

app = FastAPI()

def search_football_rules(query: str) -> str:
    """Search for football rules in the football.txt file"""
    try:
        # Simple keyword matching for football-related queries
        football_keywords = ['축구', '축구 규칙', 'football', 'soccer', '골', '경고', '오프사이드', '프리킥', '패널티킥']
        
        # Check if the query is about football
        if not any(keyword.lower() in query.lower() for keyword in football_keywords):
            return ""
            
        # Read the football rules file
        with open('/Users/<USER>/Desktop/legal-ai-tauri-final-updated/mcp_data/football.txt', 'r', encoding='utf-8') as f:
            rules = f.read()
            
        # Return the relevant section or the whole rules if it's short
        if len(rules) < 500:  # If rules are short, return all
            return f"축구 규칙에 대한 정보입니다:\n\n{rules}"
            
        # Otherwise, try to find the most relevant part
        for line in rules.split('\n'):
            if any(keyword.lower() in line.lower() for keyword in query.split()):
                return f"축구 규칙 중 관련 내용을 찾았습니다:\n\n{line}"
                
        return f"축구 규칙 중 관련 내용을 찾지 못했습니다. 전체 규칙 중 일부를 보여드립니다:\n\n{rules[:500]}..."
        
    except Exception as e:
        print(f"Error searching football rules: {e}")
        return ""

# Configure CORS - allow all origins for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"],  # Expose all headers
)

# Add middleware to log all requests
@app.middleware("http")
async def log_requests(request: Request, call_next):
    import time
    import json
    
    # Log request
    request_id = str(int(time.time()))
    print(f"\n=== Request {request_id} ===")
    print(f"Time: {time.ctime()}")
    print(f"Method: {request.method}")
    print(f"URL: {request.url}")
    print(f"Client: {request.client}")
    print(f"Headers: {dict(request.headers)}")
    
    # Log request body if present
    try:
        body = await request.body()
        if body:
            try:
                print(f"Body: {body.decode()}")
            except:
                print("Body: [binary data]")
    except Exception as e:
        print(f"Error reading request body: {e}")
    
    # Process request
    start_time = time.time()
    try:
        response = await call_next(request)
    except Exception as e:
        print(f"Error processing request: {e}")
        response = JSONResponse(
            status_code=500,
            content={"response": f"Internal server error: {str(e)}"}
        )
    
    # Calculate process time
    process_time = (time.time() - start_time) * 1000
    
    # Log response
    print(f"\n=== Response {request_id} ===")
    print(f"Process time: {process_time:.2f}ms")
    print(f"Status: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    
    # Log response body if possible
    if hasattr(response, 'body'):
        try:
            body = response.body
            if body:
                try:
                    print(f"Response: {body.decode()}")
                except:
                    print("Response: [binary data]")
        except Exception as e:
            print(f"Error reading response body: {e}")
    
    print(f"=== End of Request {request_id} ===\n")
    
    # Ensure CORS headers are set
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "*"
    response.headers["Access-Control-Allow-Headers"] = "*"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    
    return response

# Handle preflight requests
@app.options("/ask")
async def preflight_handler():
    response = Response(
        status_code=204,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, Content-Length, X-Requested-With",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400",  # 24 hours
        }
    )
    return response

@app.get("/")
async def root():
    return {"message": "Legal AI API is running. Use /ask endpoint to query the AI."}

@app.post("/ask")
async def ask(request: Request):
    print("\n=== New Request ===")
    print(f"Method: {request.method}")
    print(f"Headers: {dict(request.headers)}")
    
    try:
        # Log request body
        body = await request.body()
        # Parse JSON
        try:
            data = await request.json()
            print(f"Parsed JSON data: {data}")
            
            query = data.get("query", "").strip()
            if not query:
                return JSONResponse(
                    status_code=400,
                    content={"error": "Query parameter is required"}
                )
                
            return await process_query(query)
                
        except json.JSONDecodeError as je:
            print(f"JSON decode error: {str(je)}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid JSON format"}
            )
    except Exception as e:
        print(f"Error parsing request: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={"error": "Invalid request format"}
        )

async def query_ollama(prompt: str) -> Optional[str]:
    """Query the Ollama API with the given prompt"""
    try:
        payload = {
            "model": MODEL_NAME,
            "prompt": prompt,
            "stream": False
        }
        
        print(f"Sending request to Ollama API: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            OLLAMA_API_URL,
            json=payload,
            timeout=TIMEOUT
        )
        
        print(f"Ollama API response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Ollama API response: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response_data.get("response", "")
        else:
            print(f"Error from Ollama API: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error querying Ollama API: {e}")
        return None

async def process_query(query: str) -> Dict[str, Any]:
    """Process the user query and return a response"""
    try:
        # First, try to get football rules if the query is related to football
        football_info = search_football_rules(query)
        
        if football_info:
            # If we found relevant football info, use it to enhance the LLM response
            prompt = f"""당신은 축구 규칙에 대해 안내하는 도우미입니다. 다음 질문에 답변해주세요.
            
            질문: {query}
            
            참고 정보:
            {football_info}
            
            답변:"""
            
            llm_response = await query_ollama(prompt)
            if llm_response:
                return {"response": llm_response}
            return {"response": football_info}
        
        # For non-football queries, try to get a response from the LLM
        prompt = f"""당신은 도움이 되는 AI 어시스턴트입니다. 다음 질문에 친절하게 답변해주세요.
        
        질문: {query}
        
        답변:"""
        
        llm_response = await query_ollama(prompt)
        if llm_response:
            return {"response": llm_response}
            
        # Fallback responses if LLM is not available
        if 'hello' in query.lower() or 'hi' in query.lower() or '안녕' in query:
            return {"response": "안녕하세요! 축구 규칙에 대해 궁금하신 점이 있으신가요?"}
            
        if 'offside' in query.lower() or '오프사이드' in query:
            return {
                "response": "오프사이드 규칙은 축구에서 중요한 규칙 중 하나입니다. 기본적으로 공을 받는 선수가 상대편 진영에서 "
                         "상대편 선수들보다 골라인에 더 가까이 있을 때 오프사이드로 판정됩니다.\n\n"
                         "📌 예외 상황:\n"
                         "1. 상대편 진영에서 상대편 선수보다 뒤에 있는 경우\n"
                         "2. 공을 받는 선수가 자기 진영에 있는 경우\n"
                         "3. 골킥, 스로인, 코너킥 상황에서 공을 받는 경우\n\n"
                         "❓ 더 자세한 정보가 필요하시면 구체적으로 질문해주세요!"
            }
            
        if 'foul' in query.lower() or '파울' in query:
            return {
                "response": "⚽ 축구에서 파울은 다음과 같은 상황에서 발생합니다:\n\n"
                         "1. 상대 선수를 차거나 넘어뜨리는 행위\n"
                         "2. 손이나 팔로 공을 고의로 만지는 핸드볼\n"
                         "3. 상대 선수를 밀거나 잡는 행위\n"
                         "4. 위험한 태클\n"
                         "5. 반칙적인 방해 플레이\n\n"
                         "💡 파울 시 처벌:\n"
                         "- 일반 파울: 상대 팀에게 프리킥\n"
                         "- 페널티 에어리어 내 파울: 페널티킥"
            }
            
        # Default response for other queries
        return {
            "response": f"❓ '{query}'에 대한 정보를 찾을 수 없습니다. \n\n"
                      "⚽ 축구 규칙에 대해 궁금하신 점이 있으시면 아래 주제 중 하나를 물어보세요:\n"
                      "- 오프사이드 규칙이 뭐예요?\n"
                      "- 파울이 어떤 건가요?\n"
                      "- 경고와 퇴장 규정이 어떻게 되나요?"
        }
            
            
    except Exception as e:
        print(f"Error processing query: {e}")
        return {
            "response": "죄송합니다. 처리 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.\n\n오류 내용: " + str(e)
        }

if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)