package models

import "time"

// Message represents a chat message
type Message struct {
	Role    string `json:"role" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// ChatRequest represents the incoming chat request
type ChatRequest struct {
	Query    string    `json:"query" binding:"required"`
	Language string    `json:"language,omitempty"`
	Messages []Message `json:"messages,omitempty"`
}

// ChatResponse represents the response to a chat request
type ChatResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
}
