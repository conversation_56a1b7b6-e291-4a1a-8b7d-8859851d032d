package models

import "time"

// Message represents a chat message
type Message struct {
	Role    string `json:"role" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// ChatRequest represents the incoming chat request
type ChatRequest struct {
	Query    string    `json:"query" binding:"required"`
	Language string    `json:"language,omitempty"`
	Messages []Message `json:"messages,omitempty"`
}

// ChatResponse represents the response to a chat request
type ChatResponse struct {
	Response string `json:"response"`
	Status   string `json:"status"`
	Language string `json:"language"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
}

// FootballRule represents a parsed football rule
type FootballRule struct {
	Title       string              `json:"title"`
	Subsections []FootballSubsection `json:"subsections"`
	Keywords    []string            `json:"keywords"`
}

// FootballSubsection represents a subsection of a football rule
type FootballSubsection struct {
	Title    string   `json:"title"`
	Content  []string `json:"content"`
	Keywords []string `json:"keywords"`
}

// RuleMatch represents a matched rule with score
type RuleMatch struct {
	Title   string   `json:"title"`
	Content []string `json:"content"`
	Score   int      `json:"score"`
}

// MCPToolRequest represents a request to MCP tools
type MCPToolRequest struct {
	Tool  string                 `json:"tool"`
	Query string                 `json:"query"`
	Args  map[string]interface{} `json:"args,omitempty"`
}

// MCPToolResponse represents a response from MCP tools
type MCPToolResponse struct {
	Result string                 `json:"result"`
	Error  string                 `json:"error,omitempty"`
	Data   map[string]interface{} `json:"data,omitempty"`
}

// ProcessingResult represents the result of parallel processing
type ProcessingResult struct {
	RuleMatch   *RuleMatch       `json:"rule_match,omitempty"`
	MCPResponse *MCPToolResponse `json:"mcp_response,omitempty"`
	Error       error            `json:"error,omitempty"`
	Source      string           `json:"source"`
}
