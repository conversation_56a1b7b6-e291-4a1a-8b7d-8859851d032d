package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"football-ai-backend/handlers"
	"football-ai-backend/services"
)

func main() {
	// Set Gin mode based on environment
	if os.Getenv("GIN_MODE") == "" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Configure CORS
	config := cors.Config{
		AllowOrigins:     []string{"*"}, // Allow all origins for development
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"*"}, // Allow all headers
		ExposeHeaders:    []string{"*"}, // Expose all headers
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	router.Use(cors.New(config))

	// Initialize services
	fmt.Println("Initializing services...")
	
	// Initialize football rules service
	footballService := services.NewFootballRulesService()
	fmt.Println("✓ Football rules service initialized")

	// Initialize MCP service
	mcpService := services.NewMCPService()
	fmt.Println("✓ MCP service initialized")

	// Initialize parallel processor
	processor := services.NewParallelProcessor(footballService, mcpService)
	fmt.Println("✓ Parallel processor initialized")

	// Initialize handlers
	chatHandler := handlers.NewChatHandler(processor)
	fmt.Println("✓ Chat handler initialized")

	// Define routes
	setupRoutes(router, chatHandler)
	fmt.Println("✓ Routes configured")

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}

	// Start server
	fmt.Printf("🚀 Football AI Backend starting on port %s\n", port)
	fmt.Printf("📋 Available endpoints:\n")
	fmt.Printf("   GET  /                 - Root endpoint\n")
	fmt.Printf("   GET  /api/health       - Health check\n")
	fmt.Printf("   POST /ask              - Chat endpoint\n")
	fmt.Printf("   POST /mcp/tools        - Test MCP tools\n")
	fmt.Printf("   GET  /mcp/tools        - List available tools\n")
	fmt.Printf("🌐 Server will be available at: http://localhost:%s\n", port)

	// Start the server
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// setupRoutes configures all the routes for the application
func setupRoutes(router *gin.Engine, chatHandler *handlers.ChatHandler) {
	// Root endpoint
	router.GET("/", chatHandler.HandleRoot)

	// Health check endpoint
	router.GET("/api/health", chatHandler.HandleHealth)

	// Chat endpoints
	router.POST("/ask", chatHandler.HandleAsk)
	router.OPTIONS("/ask", chatHandler.HandleAskOptions)

	// MCP tools endpoints
	mcpGroup := router.Group("/mcp")
	{
		mcpGroup.GET("/tools", chatHandler.HandleAvailableTools)
		mcpGroup.POST("/tools", chatHandler.HandleMCPTools)
	}

	// Add a catch-all route for debugging
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "endpoint not found",
			"path":    c.Request.URL.Path,
			"method":  c.Request.Method,
			"message": "The requested endpoint does not exist",
			"available_endpoints": []string{
				"GET /",
				"GET /api/health", 
				"POST /ask",
				"OPTIONS /ask",
				"GET /mcp/tools",
				"POST /mcp/tools",
			},
		})
	})
}
