{"name": "frontend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "postcss-nesting": "^12.0.2", "tailwindcss": "^3.4.1", "vite": "^5.1.4"}}