
/* stylelint-disable */
/* This file uses Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'VT323';
  src: url('https://fonts.googleapis.com/css2?family=VT323&display=swap');
  font-weight: normal;
  font-style: normal;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 100% 20%;

    --card: 0 0% 100%;
    --card-foreground: 240 100% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 100% 20%;

    --primary: 166 100% 50%;
    --primary-foreground: 0 0% 0%;

    --secondary: 300 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 240 10% 96%;
    --muted-foreground: 240 4% 46%;

    --accent: 37 100% 50%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 166 100% 50%;
    --radius: 0.5rem;
  }


  .dark {
    --background: 240 10% 5%;
    --foreground: 0 0% 95%;

    --card: 240 10% 5%;
    --card-foreground: 0 0% 95%;

    --popover: 240 10% 5%;
    --popover-foreground: 0 0% 95%;

    --primary: 166 100% 50%;
    --primary-foreground: 0 0% 0%;

    --secondary: 300 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    --accent: 37 100% 50%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 166 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    @apply h-full;
  }

  body {
    @apply bg-background text-foreground min-h-screen flex flex-col;
    font-family: 'VT323', monospace;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Smooth theme transitions */
  body, button, input, select, textarea {
    @apply transition-colors duration-200;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/20;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full hover:bg-muted-foreground/50;
  }
}

@layer utilities {
  .retro-border {
    @apply border-4 border-primary;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.5);
  }

  .crt {
    position: relative;
    overflow: hidden;
  }

  .crt::before {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(rgba(18, 16, 16, 0) 50%, rgba(0, 0, 0, 0.1) 50%), linear-gradient(90deg, rgba(255, 0, 0, 0.03), rgba(0, 255, 0, 0.02), rgba(0, 0, 255, 0.03));
    z-index: 2;
    background-size: 100% 2px, 3px 100%;
    pointer-events: none;
  }

  .crt::after {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(18, 16, 16, 0.1);
    opacity: 0;
    z-index: 2;
    pointer-events: none;
    animation: flicker 0.15s infinite;
  }

  @keyframes flicker {
    0% {
      opacity: 0.1;
    }
    50% {
      opacity: 0.1;
    }
    51% {
      opacity: 0.08;
    }
    100% {
      opacity: 0.1;
    }
  }

  .blink {
    animation: blink 1s step-end infinite;
  }

  @keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
  }

  .pixel-spin {
    animation: pixel-spin 2s steps(8) infinite;
  }

  @keyframes pixel-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .pixel-press {
    transition: transform 0.1s step-end;
  }

  .pixel-press:active {
    transform: translateY(2px);
  }
}
