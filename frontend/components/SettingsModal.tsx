import { useState, useEffect } from 'react';
import { Moon, Sun, Monitor, X, ChevronDown } from 'lucide-react';
import { ThemeMode, LanguageCode } from '../types';
import { useTranslation } from '../hooks/useTranslation';

// Re-export types for backward compatibility
export type { ThemeMode, LanguageCode };

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentTheme: ThemeMode;
  onThemeChange: (theme: ThemeMode) => void;
  currentLanguage: LanguageCode;
  onLanguageChange: (language: LanguageCode) => void;
}

export function SettingsModal({ 
  isOpen, 
  onClose, 
  currentTheme, 
  onThemeChange,
  currentLanguage,
  onLanguageChange
}: SettingsModalProps) {
  const [mounted, setMounted] = useState(false);
  const [currentThemeState, setCurrentTheme] = useState<ThemeMode>(currentTheme);
  const { t, language, setLanguage } = useTranslation();

  // Only render on client to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    
    // Apply theme class to body when modal opens
    document.body.classList.add('modal-open');
    
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  // Handle theme change
  const handleThemeChange = (newTheme: ThemeMode) => {
    onThemeChange(newTheme);
    setCurrentTheme(newTheme);
    
    // Apply theme class to body immediately for better UX
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    if (newTheme === 'system') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.classList.add(isDark ? 'dark' : 'light');
    } else {
      root.classList.add(newTheme);
    }
    
    // Save to localStorage
    localStorage.setItem('theme', newTheme);
  };

  const handleLanguageChange = (newLanguage: string) => {
    // Type assertion is safe here because we control the select options
    const lang = newLanguage as LanguageCode;

    // Update the translation hook immediately
    setLanguage(lang);

    // Also call the parent component's handler
    onLanguageChange(lang);

    // Save to localStorage (this is also done in setLanguage, but keeping for safety)
    localStorage.setItem('language', lang);

    // Show a toast notification when language is changed
    console.log(`🌐 Language changed to: ${lang}`);
  };

  if (!mounted || !isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background p-6 rounded-lg shadow-lg w-full max-w-md border-2 border-primary">
        <div className="flex justify-between items-center mb-6 pb-4 border-b border-muted">
          <h2 className="text-2xl font-bold text-foreground">{t.settings}</h2>
          <button
            type="button"
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors"
            aria-label={t.close}
          >
            <X size={24} />
          </button>
        </div>
        <div className="space-y-6">
          {/* Theme Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-foreground">{t.theme}</h3>
            <div className="grid grid-cols-3 gap-2">
              <button
                type="button"
                onClick={() => handleThemeChange('light')}
                className={`p-3 rounded retro-border flex flex-col items-center space-y-1 ${currentThemeState === 'light' ? 'bg-accent text-accent-foreground' : 'bg-muted'}`}
                title={t.light}
              >
                <Sun size={20} />
                <span className="text-xs">{t.light}</span>
              </button>
              <button
                type="button"
                onClick={() => handleThemeChange('dark')}
                className={`p-3 rounded retro-border flex flex-col items-center space-y-1 ${currentThemeState === 'dark' ? 'bg-accent text-accent-foreground' : 'bg-muted'}`}
                title={t.dark}
              >
                <Moon size={20} />
                <span className="text-xs">{t.dark}</span>
              </button>
              <button
                type="button"
                onClick={() => handleThemeChange('system')}
                className={`p-3 rounded retro-border flex flex-col items-center space-y-1 ${currentThemeState === 'system' ? 'bg-accent text-accent-foreground' : 'bg-muted'}`}
                title={t.system}
              >
                <Monitor size={20} />
                <span className="text-xs">{t.system}</span>
              </button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Current: {currentThemeState === 'system' 
                ? 'System Default' 
                : currentThemeState === 'light' 
                  ? 'Light Mode' 
                  : 'Dark Mode'}
            </p>
          </div>

          {/* Language Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-foreground">{t.language}</h3>
            <div className="relative">
              <select
                value={language}
                onChange={(e) => handleLanguageChange(e.target.value)}
                className="w-full p-3 rounded-md border-2 border-muted bg-background text-foreground appearance-none cursor-pointer hover:border-accent/50 focus:border-accent focus:outline-none focus:ring-2 focus:ring-accent/30 transition-all"
                aria-label="Select language"
              >
                <option value="en">{t.english}</option>
                <option value="ko">{t.korean}</option>
              </select>
              <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Selected: {language === 'en' ? t.english : t.korean}
            </p>
          </div>

          {/* Close Button */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-primary text-background retro-border pixel-press"
            >
              {t.close.toUpperCase()}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
