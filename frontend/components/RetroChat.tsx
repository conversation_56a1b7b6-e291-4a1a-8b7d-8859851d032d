import { useState, useRef, useCallback, useEffect } from 'react';
import { <PERSON>troHeader } from './RetroHeader';
import { RetroConversation } from './RetroConversation';
import { RetroInput } from './RetroInput';
import { RetroMenu } from './RetroMenu';
import { CodeViewer } from './CodeViewer';
import { toast } from '@/hooks/use-toast';
import { useTranslation } from '../hooks/useTranslation';

type Message = {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  isCode?: boolean;
};

export function RetroChat() {
  const { t, language } = useTranslation();

  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: t.welcomeMessage,
      timestamp: getCurrentTime(),
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversations, setConversations] = useState<Array<{
    id: string;
    title: string;
    timestamp: string;
    messages: Message[];
  }>>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // Theme state
  const [theme, setTheme] = useState('system');

  // Code viewer state
  const [isCodeViewerOpen, setIsCodeViewerOpen] = useState(false);
  const [currentCode, setCurrentCode] = useState('');
  const [codeTitle, setCodeTitle] = useState('');
  
  // Apply theme on mount and when theme changes
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'system';

    setTheme(savedTheme);
    
    // Apply theme function
    const applyTheme = (theme: string) => {
      const root = document.documentElement;
      root.classList.remove('light', 'dark');
      
      if (theme === 'system') {
        const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.add(isDark ? 'dark' : 'light');
      } else {
        root.classList.add(theme);
      }
    };
    
    // Apply saved theme
    applyTheme(savedTheme);
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (savedTheme === 'system') {
        const root = document.documentElement;
        root.classList.remove('light', 'dark');
        root.classList.add(e.matches ? 'dark' : 'light');
      }
    };
    
    // Add event listener for system theme changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);
  
  // Update theme when it changes
  useEffect(() => {
    const applyTheme = (theme: string) => {
      const root = document.documentElement;
      root.classList.remove('light', 'dark');
      
      if (theme === 'system') {
        const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.add(isDark ? 'dark' : 'light');
      } else {
        root.classList.add(theme);
      }
      
      // Save to localStorage
      localStorage.setItem('theme', theme);
    };
    
    applyTheme(theme);
  }, [theme]);
  
  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme);
  };
  
  const handleLanguageChange = (newLanguage: string) => {
    // 번역 훅에서 언어 변경을 처리하므로 여기서는 추가 작업만 수행
    localStorage.setItem('language', newLanguage);
  };

  // Update welcome message when language changes
  useEffect(() => {
    if (messages.length === 1 && messages[0].role === 'assistant') {
      setMessages([{
        role: 'assistant',
        content: t.welcomeMessage,
        timestamp: getCurrentTime(),
      }]);
    }
  }, [language, t.welcomeMessage]);

  const audioRef = useRef<HTMLAudioElement>(null);

  // Load saved conversations from localStorage on component mount
  useEffect(() => {
    const savedConversations = localStorage.getItem('chat_conversations');
    if (savedConversations) {
      try {
        setConversations(JSON.parse(savedConversations));
      } catch (e) {
        console.error('Failed to parse saved conversations', e);
      }
    }
  }, []);

  // Auto-save conversations to localStorage whenever messages change
  useEffect(() => {
    if (messages.length > 1) { // Only save if there are actual messages (not just the welcome)
      const now = new Date();
      const conversationTitle = messages
        .find(m => m.role === 'user')?.content
        ?.substring(0, 30) || 'New Conversation';
      
      const newConversation = {
        id: currentConversationId || Date.now().toString(),
        title: conversationTitle,
        timestamp: now.toLocaleString(),
        messages: [...messages]
      };

      setConversations(prev => {
        // Update existing or add new conversation
        const existingIndex = prev.findIndex(c => c.id === currentConversationId);
        const updated = existingIndex >= 0 
          ? [...prev.slice(0, existingIndex), newConversation, ...prev.slice(existingIndex + 1)]
          : [...prev, newConversation];
        
        // Keep only the last 20 conversations
        const limited = updated.slice(-20);
        localStorage.setItem('chat_conversations', JSON.stringify(limited));
        return limited;
      });

      if (!currentConversationId) {
        setCurrentConversationId(newConversation.id);
      }
    }
  }, [messages, currentConversationId]); // Auto-save when messages or conversation ID changes

  // Auto-save is handled by the useEffect above

  // Audio function - declared first since it's used by other functions
  const playAudio = useCallback(() => {
    if (audioRef.current) {
      try {
        audioRef.current.currentTime = 0;
        const playPromise = audioRef.current.play();

        if (playPromise !== undefined) {
          playPromise.catch(error => {
            // Only log errors that aren't about the play() request being interrupted
            if (error.name !== 'AbortError' && error.name !== 'NotAllowedError') {
              console.warn('Audio playback failed:', error);
            }
          });
        }
      } catch (error) {
        console.warn('Audio playback error:', error);
      }
    }
  }, []);

  // Clear chat function - declared second since it's used by other functions
  const clearChat = useCallback(() => {
    setMessages([
      {
        role: 'assistant',
        content: t.conversationCleared,
        timestamp: getCurrentTime(),
      }
    ]);

    playAudio();
    setCurrentConversationId(null);
  }, [playAudio, t]);

  // Now we can safely use playAudio and clearChat in other functions
  const loadConversation = useCallback((conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setMessages(conversation.messages);
      setCurrentConversationId(conversationId);
      playAudio();
    }
  }, [conversations, playAudio]);

  const deleteConversation = useCallback((conversationId: string) => {
    const updatedConversations = conversations.filter(c => c.id !== conversationId);
    setConversations(updatedConversations);
    localStorage.setItem('chat_conversations', JSON.stringify(updatedConversations));

    // If the deleted conversation is currently loaded, clear the chat
    if (currentConversationId === conversationId) {
      clearChat();
    }

    playAudio();
  }, [conversations, currentConversationId, clearChat, playAudio]);

  const clearAllConversations = useCallback(() => {
    setConversations([]);
    localStorage.removeItem('chat_conversations');
    clearChat();
    playAudio();
  }, [clearChat, playAudio]);

  function getCurrentTime() {
    const now = new Date();
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  }

  // 코드 응답인지 감지하는 함수
  const isCodeResponse = (content: string): boolean => {
    return content.includes('```') ||
           content.includes('**Python') ||
           content.includes('**JavaScript') ||
           content.includes('**Code**') ||
           content.includes('def ') ||
           content.includes('import ') ||
           content.includes('class ') ||
           content.includes('function ');
  };

  // 코드 제목 추출 함수
  const extractCodeTitle = (content: string): string => {
    const titleMatch = content.match(/\*\*(.*?Code.*?)\*\*/);
    if (titleMatch) return titleMatch[1];

    if (content.includes('snake')) return 'Snake Game Code';
    if (content.includes('calculator')) return 'Calculator Code';
    if (content.includes('todo')) return 'Todo App Code';
    if (content.includes('scraper')) return 'Web Scraper Code';

    return 'Generated Code';
  };

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    playAudio();

    const userMessage = {
      role: 'user' as const,
      content: message,
      timestamp: getCurrentTime(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // 언어 설정 - 번역 훅에서 가져오기

    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
      setIsLoading(false);
      toast({
        title: "Timeout",
        description: "서버 응답이 지연되고 있습니다. 다시 시도해 주세요.",
        variant: "destructive",
      });
    }, 10000);

    try {
      const apiUrl = "/api/ask";
      const requestBody = { 
        query: message, 
        language: language || 'en', 
        messages: messages.slice(-10).map(m => ({
          role: m.role,
          content: m.content
        }))
      };

      console.log('Sending request to:', apiUrl);
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const res = await fetch(apiUrl, {
        method: "POST",
        mode: "cors",
        credentials: 'include',
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      console.log('Response status:', res.status);
      console.log('Response headers:', Object.fromEntries(res.headers.entries()));
      
      let responseData: Record<string, any> = {};
      
      if (!res.ok) {
        const errorText = await res.text();
        console.error('Server responded with error:', errorText);
        throw new Error(`HTTP error! status: ${res.status}, body: ${errorText}`);
      }
      try {
        const responseText = await res.text();
        console.log('Raw response text:', responseText);
        
        // Try to parse as JSON, but handle case where response might be plain text
        try {
          responseData = JSON.parse(responseText);
          console.log('Parsed response data:', responseData);
        } catch (e) {
          console.log('Response is not JSON, using as plain text');
          responseData = { answer: responseText };
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Error processing response:', error);
        throw new Error(`Failed to process response: ${errorMessage}`);
      }

      // Handle different response formats
      let responseText: string;
      
      if (typeof responseData === 'string') {
        // If the response is a string, use it directly
        responseText = responseData;
      } else if (responseData.answer) {
        // If there's an answer field, use it
        responseText = responseData.answer;
      } else if (responseData.response) {
        // If there's a response field, use it
        responseText = responseData.response;
      } else if (responseData.text) {
        // If there's a text field, use it
        responseText = responseData.text;
      } else if (responseData.choices && Array.isArray(responseData.choices) && responseData.choices.length > 0) {
        // Handle OpenAI-style response with choices
        responseText = responseData.choices[0].message?.content || 
                      responseData.choices[0].text || 
                      JSON.stringify(responseData.choices[0]);
      } else {
        // Fallback to stringifying the whole response
        console.warn('Unexpected response format:', responseData);
        responseText = typeof responseData === 'object' 
          ? JSON.stringify(responseData, null, 2) 
          : String(responseData);
      }

      const assistantMessage = {
        role: 'assistant' as const,
        content: responseText.trim() || "I'm sorry, I couldn't generate a response. Please try again.",
        timestamp: getCurrentTime(),
        isCode: isCodeResponse(responseText),
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 코드 응답인 경우 코드 뷰어 자동 열기
      if (isCodeResponse(responseText)) {
        setCurrentCode(responseText);
        setCodeTitle(extractCodeTitle(responseText));
        setIsCodeViewerOpen(true);
      }
    } catch (error) {
      clearTimeout(timeoutId);
      console.error("API call failed:", error);

      // Create error message
      const errorMessage = {
        role: 'assistant' as const,
        content: "I'm having trouble connecting to the AI server. Please check your internet connection and try again.",
        timestamp: getCurrentTime(),
      };

      // Use functional update to ensure we're working with the latest state
      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Connection Error",
        description: error instanceof Error ? error.message : "Failed to connect to the AI server. Please try again.",
        variant: "destructive",
      });
    } finally {
      setTimeout(() => {
        setIsLoading(false);
        playAudio();
      }, 0);
    }
  };



  // Handle any errors
  // Remove unused showError function

  // 디버깅용: isLoading 상태 변화 콘솔 출력
  useEffect(() => {
    console.log('isLoading 상태:', isLoading);
  }, [isLoading]);

  // ESC 키로 코드 뷰어 닫기
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isCodeViewerOpen) {
        setIsCodeViewerOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isCodeViewerOpen]);

  return (
    <div className="w-full max-w-6xl h-full flex flex-col md:flex-row gap-4 relative">
      <div className={`flex flex-col gap-4 transition-all duration-300 ${
        isCodeViewerOpen ? 'flex-1' : 'flex-1'
      }`}>
        <RetroHeader />
        <RetroConversation messages={messages} isLoading={isLoading} />
        <RetroInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
        />
        {/* 디버깅용 강제 해제 버튼 - 개발 모드에서만 표시 */}
        {process.env.NODE_ENV === 'development' && (
          <button
            type="button"
            onClick={() => setIsLoading(false)}
            className="fixed bottom-2 right-2 z-50 px-2 py-1 text-xs bg-red-600 text-white rounded opacity-50 hover:opacity-100"
            title="Force unlock loading state (dev only)"
          >
            FORCE UNLOCK
          </button>
        )}
      </div>

      {/* 메뉴는 코드 뷰어가 열려있을 때 숨김 */}
      {!isCodeViewerOpen && (
        <RetroMenu
          onClearChat={clearChat}
          onLoadConversation={loadConversation}
          onDeleteConversation={deleteConversation}
          onClearAllConversations={clearAllConversations}
          conversations={conversations}
          onThemeChange={handleThemeChange}
          onLanguageChange={handleLanguageChange}
        />
      )}

      {/* 코드 뷰어 */}
      <CodeViewer
        isOpen={isCodeViewerOpen}
        onClose={() => setIsCodeViewerOpen(false)}
        code={currentCode}
        title={codeTitle}
      />

      <audio ref={audioRef} src="/sounds/silent.mp3" preload="auto" />
    </div>
  );
}