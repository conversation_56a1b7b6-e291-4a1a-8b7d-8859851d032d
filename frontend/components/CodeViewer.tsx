import { useState, useEffect, useRef } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface CodeViewerProps {
  isOpen: boolean;
  onClose: () => void;
  code: string;
  language?: string;
  title?: string;
}

export function CodeViewer({ isOpen, onClose, code, language = 'python', title = 'Generated Code' }: CodeViewerProps) {
  const [displayedCode, setDisplayedCode] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [copied, setCopied] = useState(false);
  const { t } = useTranslation();
  const streamingRef = useRef<NodeJS.Timeout>();

  // 스트리밍 타이핑 효과
  useEffect(() => {
    if (isOpen && code && code !== displayedCode) {
      setIsStreaming(true);
      setDisplayedCode('');
      
      let currentIndex = 0;
      const streamCode = () => {
        if (currentIndex < code.length) {
          setDisplayedCode(prev => prev + code[currentIndex]);
          currentIndex++;
          streamingRef.current = setTimeout(streamCode, 10); // 10ms 간격으로 타이핑
        } else {
          setIsStreaming(false);
        }
      };
      
      streamCode();
    }

    return () => {
      if (streamingRef.current) {
        clearTimeout(streamingRef.current);
      }
    };
  }, [isOpen, code]);

  // 코드 복사 기능
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // 코드에서 언어 추출
  const extractLanguageFromCode = (codeContent: string): string => {
    const match = codeContent.match(/```(\w+)/);
    return match ? match[1] : language;
  };

  // 코드 블록에서 실제 코드만 추출
  const extractCodeContent = (codeContent: string): string => {
    const codeBlockMatch = codeContent.match(/```\w*\n([\s\S]*?)\n```/);
    return codeBlockMatch ? codeBlockMatch[1] : codeContent;
  };

  const actualCode = extractCodeContent(code);
  const detectedLanguage = extractLanguageFromCode(code);

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-y-0 right-0 w-1/2 bg-background border-l-2 border-primary transform transition-transform duration-300 ease-in-out z-50 ${
      isOpen ? 'translate-x-0' : 'translate-x-full'
    }`}>
      {/* Header */}
      <div className="bg-primary text-background p-4 retro-border-bottom flex justify-between items-center">
        <div>
          <h2 className="text-lg font-bold">{title}</h2>
          <p className="text-sm opacity-80">{detectedLanguage.toUpperCase()}</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleCopy}
            className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
            title="Copy code"
          >
            {copied ? '✓ COPIED' : '📋 COPY'}
          </button>
          <button
            onClick={onClose}
            className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
            title="Close code viewer"
          >
            ✕ CLOSE
          </button>
        </div>
      </div>

      {/* Code Content */}
      <div className="h-full overflow-y-auto p-4 bg-muted">
        <pre className="text-sm font-mono whitespace-pre-wrap break-words">
          <code className={`language-${detectedLanguage}`}>
            {displayedCode}
            {isStreaming && <span className="blink">_</span>}
          </code>
        </pre>
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 bg-primary text-background p-2 text-xs">
        <div className="flex justify-between items-center">
          <span>
            {isStreaming ? 'Generating code...' : `${actualCode.split('\n').length} lines`}
          </span>
          <span>
            Press ESC to close
          </span>
        </div>
      </div>
    </div>
  );
}
