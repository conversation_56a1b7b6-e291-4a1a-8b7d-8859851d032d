import { useState, useEffect, useRef } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface CodeViewerProps {
  isOpen: boolean;
  onClose: () => void;
  code: string;
  language?: string;
  title?: string;
}

export function CodeViewer({ isOpen, onClose, code, language = 'python', title = 'Generated Code' }: CodeViewerProps) {
  const [displayedCode, setDisplayedCode] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [copied, setCopied] = useState(false);
  const { t } = useTranslation();
  const streamingRef = useRef<NodeJS.Timeout>();

  // 스트리밍 타이핑 효과
  useEffect(() => {
    if (isOpen && code && code !== displayedCode) {
      setIsStreaming(true);
      setDisplayedCode('');
      
      let currentIndex = 0;
      const streamCode = () => {
        if (currentIndex < code.length) {
          setDisplayedCode(prev => prev + code[currentIndex]);
          currentIndex++;
          streamingRef.current = setTimeout(streamCode, 10); // 10ms 간격으로 타이핑
        } else {
          setIsStreaming(false);
        }
      };
      
      streamCode();
    }

    return () => {
      if (streamingRef.current) {
        clearTimeout(streamingRef.current);
      }
    };
  }, [isOpen, code]);

  // 코드 복사 기능
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // 코드에서 언어 추출
  const extractLanguageFromCode = (codeContent: string): string => {
    const match = codeContent.match(/```(\w+)/);
    return match ? match[1] : language;
  };

  // 코드 블록에서 실제 코드만 추출
  const extractCodeContent = (codeContent: string): string => {
    const codeBlockMatch = codeContent.match(/```\w*\n([\s\S]*?)\n```/);
    return codeBlockMatch ? codeBlockMatch[1] : codeContent;
  };

  const actualCode = extractCodeContent(code);
  const detectedLanguage = extractLanguageFromCode(code);

  if (!isOpen) return null;

  return (
    <div className="flex flex-col gap-4 w-full animate-in slide-in-from-right duration-500">
      {/* Code Viewer Header - 대화창과 동일한 스타일 */}
      <div className="bg-primary text-background p-4 retro-border">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <span className="text-accent">⚡</span>
              CODE TERMINAL
              {isStreaming && <span className="text-xs animate-pulse">GENERATING...</span>}
            </h2>
            <p className="text-sm opacity-80">{title} | {detectedLanguage.toUpperCase()}</p>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleCopy}
              className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
              title="Copy code"
            >
              {copied ? '✓ COPIED' : '📋 COPY'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
              title="Close code viewer"
            >
              ✕ CLOSE
            </button>
          </div>
        </div>
      </div>

      {/* Code Content - 대화창과 동일한 스타일 + 터미널 효과 */}
      <div className="flex-1 overflow-y-auto p-4 retro-border bg-background code-terminal relative">
        <div className="mb-2">
          <div className="flex items-center mb-1">
            <span className="text-primary font-bold mr-2">CODE&gt;</span>
            <span className="text-secondary text-xs">
              {isStreaming ? 'Generating...' : `${actualCode.split('\n').length} lines`}
            </span>
            {isStreaming && (
              <div className="ml-2 flex items-center gap-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-1"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-2"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-3"></div>
              </div>
            )}
          </div>
        </div>

        <pre className="text-sm font-mono whitespace-pre-wrap break-words text-foreground relative z-10">
          <code className={`language-${detectedLanguage}`}>
            {displayedCode}
            {isStreaming && <span className="blink text-primary">█</span>}
          </code>
        </pre>
      </div>

      {/* Code Actions - 입력창과 동일한 스타일 */}
      <div className="p-4 retro-border bg-background">
        <div className="flex justify-between items-center">
          <div className="text-xs text-secondary">
            {isStreaming ? 'GENERATING CODE...' : 'CODE READY'} | Press ESC to close
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleCopy}
              className="bg-primary text-background px-4 py-1 retro-border pixel-press text-sm"
              title="Copy all code"
            >
              {copied ? 'COPIED!' : 'COPY ALL'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="bg-secondary text-background px-4 py-1 retro-border pixel-press text-sm"
              title="Close code viewer"
            >
              CLOSE
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
