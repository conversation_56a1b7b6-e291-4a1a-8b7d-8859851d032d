import { useEffect } from 'react';
import { RetroHeader } from './RetroHeader';
import { RetroConversation } from './RetroConversation';
import { RetroInput } from './RetroInput';
import { RetroMenu } from './RetroMenu';
import { LivePreview } from './LivePreview';
import { useChat } from '../hooks/useChat';

export function RetroChatRefactored() {
  const {
    // State
    messages,
    isLoading,
    conversations,
    isCodeViewerOpen,
    currentCode,
    codeTitle,
    
    // Refs
    audioRef,
    
    // Handlers
    handleSendMessage,
    clearChat,
    loadConversation,
    deleteConversation,
    clearAllConversations,
    handleThemeChange,
    handleLanguageChange,
    handleCloseCodeViewer,
    initialize,
  } = useChat();

  // Initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  // ESC 키로 코드 뷰어 닫기
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isCodeViewerOpen) {
        handleCloseCodeViewer();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isCodeViewerOpen, handleCloseCodeViewer]);

  return (
    <div className="w-full max-w-7xl h-full flex flex-col md:flex-row gap-4 relative">
      {/* 왼쪽 대화 영역 */}
      <div className={`flex flex-col gap-4 transition-all duration-500 ease-in-out chat-layout-left ${
        isCodeViewerOpen ? 'md:w-1/2' : 'flex-1'
      }`}>
        <RetroHeader />
        <RetroConversation messages={messages} isLoading={isLoading} />
        <RetroInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
        />
        {/* 디버깅용 강제 해제 버튼 - 개발 모드에서만 표시 */}
        {process.env.NODE_ENV === 'development' && (
          <button
            type="button"
            onClick={() => {/* setIsLoading(false) */}}
            className="fixed bottom-2 right-2 z-50 px-2 py-1 text-xs bg-red-600 text-white rounded opacity-50 hover:opacity-100"
            title="Force unlock loading state (dev only)"
          >
            FORCE UNLOCK
          </button>
        )}
      </div>

      {/* 오른쪽 영역 - 코드 뷰어 또는 메뉴 */}
      <div className={`transition-all duration-500 ease-in-out chat-layout-right ${
        isCodeViewerOpen ? 'md:w-1/2' : 'w-full md:w-80'
      }`}>
        {isCodeViewerOpen ? (
          /* 라이브 미리보기 */
          <LivePreview
            isOpen={isCodeViewerOpen}
            onClose={handleCloseCodeViewer}
            code={currentCode}
            title={codeTitle}
          />
        ) : (
          /* 메뉴 */
          <RetroMenu
            onClearChat={clearChat}
            onLoadConversation={loadConversation}
            onDeleteConversation={deleteConversation}
            onClearAllConversations={clearAllConversations}
            conversations={conversations}
            onThemeChange={handleThemeChange}
            onLanguageChange={handleLanguageChange}
          />
        )}
      </div>

      <audio ref={audioRef} src="/sounds/silent.mp3" preload="auto" />
    </div>
  );
}
