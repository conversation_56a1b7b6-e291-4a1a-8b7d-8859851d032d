import { useState } from 'react';

interface RetroInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export function RetroInput({ onSendMessage, isLoading }: RetroInputProps) {
  const [input, setInput] = useState('');

  return (
    <div className="p-4 retro-border bg-background">
      <textarea
        className="w-full bg-muted text-primary p-2 retro-border resize-none focus:outline-none"
        rows={3}
        value={input}
        onChange={(e) => setInput(e.target.value)}
      />
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs text-secondary">
          READY
        </div>
        <button
          className="bg-primary text-background px-4 py-1 retro-border pixel-press"
          onClick={() => {
            if (input.trim()) {
              onSendMessage(input);
              setInput('');
            }
          }}
        >
          SEND
        </button>
      </div>
    </div>
  );
}
