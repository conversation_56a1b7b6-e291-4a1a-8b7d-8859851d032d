import { useState } from 'react';

interface RetroInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export function RetroInput({ onSendMessage, isLoading }: RetroInputProps) {
  const [input, setInput] = useState('');

  const handleSend = () => {
    if (input.trim() && !isLoading) {
      onSendMessage(input);
      setInput('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // 기본 엔터 동작(줄바꿈) 방지
      handleSend();
    }
    // Shift + Enter는 줄바꿈으로 동작
  };

  return (
    <div className="p-4 retro-border bg-background">
      <textarea
        className="w-full bg-muted text-primary p-2 retro-border resize-none focus:outline-none"
        rows={3}
        value={input}
        onChange={(e) => setInput(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Type your question here... (Press Enter to send, Shift+Enter for new line)"
        disabled={isLoading}
        aria-label="Question input"
      />
      <div className="flex justify-between items-center mt-2">
        <div className="text-xs text-secondary">
          {isLoading ? 'PROCESSING...' : 'READY'} | Press Enter to send
        </div>
        <button
          type="button"
          className="bg-primary text-background px-4 py-1 retro-border pixel-press"
          onClick={handleSend}
          disabled={isLoading || !input.trim()}
          title="Send message (or press Enter)"
        >
          {isLoading ? 'SENDING...' : 'SEND'}
        </button>
      </div>
    </div>
  );
}
