import { useState, useEffect, useRef } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface LivePreviewProps {
  isOpen: boolean;
  onClose: () => void;
  code: string;
  language?: string;
  title?: string;
}

export function LivePreview({ isOpen, onClose, code, language = 'python', title = 'Live Preview' }: LivePreviewProps) {
  const [previewContent, setPreviewContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 코드에서 언어 추출
  const extractLanguageFromCode = (codeContent: string): string => {
    const match = codeContent.match(/```(\w+)/);
    return match ? match[1] : language;
  };

  // 코드 블록에서 실제 코드만 추출
  const extractCodeContent = (codeContent: string): string => {
    const codeBlockMatch = codeContent.match(/```\w*\n([\s\S]*?)\n```/);
    return codeBlockMatch ? codeBlockMatch[1] : codeContent;
  };

  // HTML/CSS/JS 코드를 실행 가능한 HTML로 변환
  const generateHTMLPreview = (codeContent: string, detectedLanguage: string): string => {
    if (detectedLanguage === 'html') {
      return codeContent;
    } else if (detectedLanguage === 'javascript' || detectedLanguage === 'js') {
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JavaScript Preview</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>JavaScript Code Preview</h1>
    <div id="output"></div>
    <div class="console" id="console"></div>
    
    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const consoleDiv = document.getElementById('console');
            consoleDiv.innerHTML += args.join(' ') + '\\n';
        };
        
        try {
            ${codeContent}
        } catch (error) {
            console.log('Error: ' + error.message);
        }
    </script>
</body>
</html>`;
    } else if (detectedLanguage === 'python') {
      return generatePythonPreview(codeContent);
    } else {
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Code Preview</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        pre { background: #000; color: #0f0; padding: 20px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Code Preview</h1>
    <pre><code>${codeContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
</body>
</html>`;
    }
  };

  // 코드에서 실제 제목 추출
  const extractTitleFromCode = (codeContent: string, defaultTitle: string): string => {
    // pygame.display.set_caption에서 제목 추출 (Snake Game용)
    const captionMatch = codeContent.match(/pygame\.display\.set_caption\(['"]([^'"]+)['"]\)/);
    if (captionMatch) {
      return captionMatch[1];
    }

    // print 문에서 제목 추출 (Calculator, Todo App, Web Scraper용)
    const printMatches = [
      /print\(['"]([^'"]*Calculator[^'"]*)['"]\)/i,
      /print\(['"]([^'"]*Todo[^'"]*)['"]\)/i,
      /print\(['"]([^'"]*Scraper[^'"]*)['"]\)/i,
      /print\(['"]([^'"]*Demo[^'"]*)['"]\)/i
    ];

    for (const pattern of printMatches) {
      const match = codeContent.match(pattern);
      if (match) {
        return match[1];
      }
    }

    // 주석에서 제목 추출
    const commentMatches = [
      /# ([^#\n]*Game[^#\n]*)/i,
      /# ([^#\n]*Calculator[^#\n]*)/i,
      /# ([^#\n]*Todo[^#\n]*)/i,
      /# ([^#\n]*Scraper[^#\n]*)/i
    ];

    for (const pattern of commentMatches) {
      const match = codeContent.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return defaultTitle;
  };

  // Python 코드 미리보기 생성
  const generatePythonPreview = (codeContent: string): string => {
    let previewHTML = `
    <div style="font-family: Arial, sans-serif; padding: 20px; background: #1e1e1e; color: #fff; border-radius: 8px;">
        <div style="background: #2d2d2d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h1 style="margin: 0 0 10px 0;">🐍 Python Code Preview</h1>
            <p style="margin: 0; opacity: 0.8;">Live preview of your Python application</p>
        </div>`;

    // Snake Game 감지
    if (codeContent.includes('pygame') && (codeContent.includes('Snake') || codeContent.includes('snake'))) {
      const gameTitle = extractTitleFromCode(codeContent, 'Snake Game');
      previewHTML += `
        <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <h2 style="margin: 0 0 20px 0; color: #0f0;">🎮 ${gameTitle} Preview</h2>
            <div style="width: 100%; max-width: 500px; height: 350px; background: #000; border: 3px solid #0f0; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: #0f0; position: relative; overflow: hidden;">
                <div style="position: absolute; top: 10px; left: 10px; font-size: 12px; color: #0f0;">Score: 0</div>
                <div style="position: absolute; top: 10px; right: 10px; font-size: 12px; color: #0f0;">Level: 1</div>
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 10px;">🐍</div>
                    <div style="font-size: 18px; margin-bottom: 5px;">${gameTitle}</div>
                    <div style="font-size: 12px; opacity: 0.7;">800x600 Game Window</div>
                    <div style="margin-top: 20px; font-size: 14px;">
                        <div>🍎 Food</div>
                        <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">Use Arrow Keys to Move</div>
                    </div>
                </div>
                <div style="position: absolute; bottom: 10px; left: 50%; transform: translateX(-50%); font-size: 10px; opacity: 0.6;">Pygame Window Simulation</div>
            </div>
        </div>`;
    }
    // Calculator 감지
    else if (codeContent.includes('calculator') || codeContent.includes('eval')) {
      const calcTitle = extractTitleFromCode(codeContent, 'Simple Calculator');
      previewHTML += `
        <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <h2 style="margin: 0 0 20px 0; color: #58a6ff;">🧮 ${calcTitle} Preview</h2>
            <div style="width: 100%; max-width: 400px; background: #000; border: 3px solid #58a6ff; margin: 0 auto; padding: 20px; border-radius: 8px; font-family: monospace;">
                <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <div style="color: #0f0; font-size: 16px; margin-bottom: 10px;">${calcTitle}</div>
                    <div style="color: #58a6ff; font-size: 12px;">Operations: +, -, *, /, **, %</div>
                </div>
                <div style="background: #0a0a0a; padding: 15px; border-radius: 5px; text-align: left;">
                    <div style="color: #fbbf24; margin-bottom: 8px;">Enter calculation:</div>
                    <div style="color: #fff; margin-bottom: 8px;">&gt; 2 + 3</div>
                    <div style="color: #7dd3fc; margin-bottom: 15px;">Result: 5</div>
                    <div style="color: #fbbf24; margin-bottom: 8px;">Enter calculation:</div>
                    <div style="color: #fff; margin-bottom: 8px;">&gt; 10 ** 2</div>
                    <div style="color: #7dd3fc; margin-bottom: 15px;">Result: 100</div>
                    <div style="color: #fbbf24;">Enter calculation:</div>
                    <div style="color: #fff;">&gt; <span style="animation: blink 1s infinite;">_</span></div>
                </div>
            </div>
        </div>`;
    }
    // Todo App 감지
    else if (codeContent.includes('todo') || codeContent.includes('TodoApp')) {
      const todoTitle = extractTitleFromCode(codeContent, 'Todo App');
      previewHTML += `
        <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <h2 style="margin: 0 0 20px 0; color: #7dd3fc;">📝 ${todoTitle} Preview</h2>
            <div style="width: 100%; max-width: 450px; background: #000; border: 3px solid #7dd3fc; margin: 0 auto; padding: 20px; border-radius: 8px; font-family: monospace;">
                <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <div style="color: #0f0; font-size: 16px; margin-bottom: 10px;">📝 ${todoTitle}</div>
                    <div style="color: #58a6ff; font-size: 12px; text-align: left;">
                        <div>1. Add todo</div>
                        <div>2. List todos</div>
                        <div>3. Complete todo</div>
                        <div>4. Delete todo</div>
                        <div>5. Quit</div>
                    </div>
                </div>
                <div style="background: #0a0a0a; padding: 15px; border-radius: 5px; text-align: left;">
                    <div style="color: #fbbf24; margin-bottom: 8px;">Choose option (1-5): <span style="color: #fff;">1</span></div>
                    <div style="color: #7dd3fc; margin-bottom: 8px;">Enter new todo: Learn Python</div>
                    <div style="color: #7dd3fc; margin-bottom: 15px;">Added: Learn Python</div>
                    <div style="color: #fbbf24; margin-bottom: 8px;">Choose option (1-5): <span style="color: #fff;">2</span></div>
                    <div style="color: #7dd3fc; margin-bottom: 8px;">Your todos:</div>
                    <div style="color: #fff; margin-bottom: 8px;">1. ○ Learn Python</div>
                    <div style="color: #fbbf24;">Choose option (1-5): <span style="animation: blink 1s infinite;">_</span></div>
                </div>
            </div>
        </div>`;
    }
    // Web Scraper 감지
    else if (codeContent.includes('requests') && codeContent.includes('BeautifulSoup')) {
      const scraperTitle = extractTitleFromCode(codeContent, 'Web Scraper Demo');
      previewHTML += `
        <div style="background: #2d2d2d; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
            <h2 style="margin: 0 0 20px 0; color: #fbbf24;">🌐 ${scraperTitle} Preview</h2>
            <div style="width: 100%; max-width: 500px; background: #000; border: 3px solid #fbbf24; margin: 0 auto; padding: 20px; border-radius: 8px; font-family: monospace;">
                <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <div style="color: #0f0; font-size: 16px; margin-bottom: 10px;">🕷️ ${scraperTitle}</div>
                    <div style="color: #58a6ff; font-size: 12px;">Scraping quotes.toscrape.com</div>
                </div>
                <div style="background: #0a0a0a; padding: 15px; border-radius: 5px; text-align: left;">
                    <div style="color: #58a6ff; margin-bottom: 8px;">1. Scraping quotes...</div>
                    <div style="color: #7dd3fc; margin-bottom: 15px;">Found 10 quotes</div>
                    <div style="color: #fbbf24; margin-bottom: 8px; font-size: 11px;">"The way to get started is to quit talking and begin doing."</div>
                    <div style="color: #fff; margin-bottom: 15px; font-size: 10px;">- Walt Disney</div>
                    <div style="color: #fbbf24; margin-bottom: 8px; font-size: 11px;">"Life is what happens to you while you're busy making other plans."</div>
                    <div style="color: #fff; margin-bottom: 15px; font-size: 10px;">- John Lennon</div>
                    <div style="color: #7dd3fc; margin-bottom: 8px;">Saved to quotes.json</div>
                    <div style="color: #0f0;">✓ Scraping completed</div>
                </div>
            </div>
        </div>`;
    }
    else {
      previewHTML += `
        <div style="background: #2d2d2d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0 0 15px 0;">🐍 Python Code</h2>
            <pre style="background: #0d1117; color: #58a6ff; padding: 10px; border-radius: 5px; overflow: auto; font-size: 12px; margin: 0;">${codeContent.substring(0, 500)}${codeContent.length > 500 ? '...' : ''}</pre>
        </div>`;
    }

    previewHTML += `
    </div>`;

    return previewHTML;
  };

  // 코드 변경 시 미리보기 업데이트
  useEffect(() => {
    if (isOpen && code) {
      setIsLoading(true);
      const actualCode = extractCodeContent(code);
      const detectedLanguage = extractLanguageFromCode(code);
      
      // 디바운싱: 500ms 후에 업데이트
      const timer = setTimeout(() => {
        const htmlContent = generateHTMLPreview(actualCode, detectedLanguage);
        setPreviewContent(htmlContent);
        setIsLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [code, isOpen]);

  // iframe 콘텐츠는 이제 srcDoc으로 직접 설정됨

  if (!isOpen) return null;

  return (
    <div className="flex flex-col gap-4 w-full h-full animate-in slide-in-from-right duration-500">
      {/* Live Preview Header */}
      <div className="bg-primary text-background p-4 retro-border flex-shrink-0">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <span className="text-accent">🔴</span>
              LIVE PREVIEW
              {isLoading && <span className="text-xs animate-pulse">UPDATING...</span>}
            </h2>
            <p className="text-sm opacity-80">{title} | Real-time execution</p>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onClose}
              className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
              title="Close live preview"
            >
              ✕ CLOSE
            </button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 retro-border bg-background relative overflow-hidden">
        <div className="p-2 bg-muted border-b flex-shrink-0">
          <div className="flex items-center mb-1">
            <span className="text-primary font-bold mr-2">PREVIEW&gt;</span>
            <span className="text-secondary text-xs">
              {isLoading ? 'Updating...' : 'Live'}
            </span>
            {isLoading && (
              <div className="ml-2 flex items-center gap-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-1"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-2"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-3"></div>
              </div>
            )}
          </div>
        </div>

        {previewContent ? (
          <div
            className="w-full h-full overflow-auto p-4 preview-content"
            dangerouslySetInnerHTML={{ __html: previewContent }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-secondary">
            <div className="text-center">
              <div className="text-2xl mb-2">⚡</div>
              <div>Waiting for code...</div>
            </div>
          </div>
        )}
      </div>

      {/* Preview Status */}
      <div className="p-4 retro-border bg-background flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="text-xs text-secondary">
            {isLoading ? 'UPDATING PREVIEW...' : 'PREVIEW READY'} | Real-time sync enabled
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onClose}
              className="bg-secondary text-background px-4 py-1 retro-border pixel-press text-sm"
              title="Close preview"
            >
              CLOSE
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
