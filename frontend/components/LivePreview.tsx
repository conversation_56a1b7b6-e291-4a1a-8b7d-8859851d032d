import { useState, useEffect, useRef } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface LivePreviewProps {
  isOpen: boolean;
  onClose: () => void;
  code: string;
  language?: string;
  title?: string;
}

export function LivePreview({ isOpen, onClose, code, language = 'python', title = 'Live Preview' }: LivePreviewProps) {
  const [previewContent, setPreviewContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 코드에서 언어 추출
  const extractLanguageFromCode = (codeContent: string): string => {
    const match = codeContent.match(/```(\w+)/);
    return match ? match[1] : language;
  };

  // 코드 블록에서 실제 코드만 추출
  const extractCodeContent = (codeContent: string): string => {
    const codeBlockMatch = codeContent.match(/```\w*\n([\s\S]*?)\n```/);
    return codeBlockMatch ? codeBlockMatch[1] : codeContent;
  };

  // HTML/CSS/JS 코드를 실행 가능한 HTML로 변환
  const generateHTMLPreview = (codeContent: string, detectedLanguage: string): string => {
    if (detectedLanguage === 'html') {
      return codeContent;
    } else if (detectedLanguage === 'javascript' || detectedLanguage === 'js') {
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JavaScript Preview</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>JavaScript Code Preview</h1>
    <div id="output"></div>
    <div class="console" id="console"></div>
    
    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const consoleDiv = document.getElementById('console');
            consoleDiv.innerHTML += args.join(' ') + '\\n';
        };
        
        try {
            ${codeContent}
        } catch (error) {
            console.log('Error: ' + error.message);
        }
    </script>
</body>
</html>`;
    } else if (detectedLanguage === 'python') {
      return generatePythonPreview(codeContent);
    } else {
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Code Preview</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        pre { background: #000; color: #0f0; padding: 20px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Code Preview</h1>
    <pre><code>${codeContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
</body>
</html>`;
    }
  };

  // Python 코드 미리보기 생성
  const generatePythonPreview = (codeContent: string): string => {
    let previewHTML = `
    <div style="font-family: Arial, sans-serif; padding: 20px; background: #1e1e1e; color: #fff; border-radius: 8px;">
        <div style="background: #2d2d2d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h1 style="margin: 0 0 10px 0;">🐍 Python Code Preview</h1>
            <p style="margin: 0; opacity: 0.8;">Live preview of your Python application</p>
        </div>`;

    // Snake Game 감지
    if (codeContent.includes('pygame') && codeContent.includes('Snake')) {
      previewHTML += `
        <div style="background: #2d2d2d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0 0 15px 0;">🎮 Snake Game</h2>
            <div style="width: 400px; height: 300px; background: #000; border: 2px solid #0f0; margin: 10px 0; display: flex; align-items: center; justify-content: center; color: #0f0;">
                <div style="text-align: center;">
                    <div style="font-size: 24px;">🐍</div>
                    <div>Snake Game</div>
                    <div style="font-size: 12px; margin-top: 10px;">800x600 pixels</div>
                </div>
            </div>
            <div style="background: #1a1a1a; padding: 10px; border-radius: 5px;">
                <strong>Controls:</strong> Arrow Keys | <strong>Goal:</strong> Eat red food to grow
            </div>
        </div>

        <div style="background: #0d1117; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0;">📋 Code Analysis</h3>
            <div style="margin: 5px 0; color: #7dd3fc;">✅ Pygame initialization</div>
            <div style="margin: 5px 0; color: #7dd3fc;">✅ Snake class with movement logic</div>
            <div style="margin: 5px 0; color: #7dd3fc;">✅ Food generation system</div>
            <div style="margin: 5px 0; color: #7dd3fc;">✅ Collision detection</div>
            <div style="margin: 5px 0; color: #7dd3fc;">✅ Game loop with event handling</div>
        </div>`;
    }
    // Calculator 감지
    else if (codeContent.includes('calculator') || codeContent.includes('eval')) {
      previewHTML += `
    <div class="preview-section">
        <h2>🧮 Calculator</h2>
        <div style="background: #000; padding: 20px; border-radius: 8px; font-family: monospace;">
            <div style="color: #0f0;">Simple Calculator</div>
            <div style="color: #58a6ff; margin: 10px 0;">Operations: +, -, *, /, ** (power), % (modulo)</div>
            <div style="color: #fbbf24;">Enter calculation (or "quit" to exit): <span style="color: #fff;">2 + 3</span></div>
            <div style="color: #7dd3fc;">Result: 5</div>
        </div>
    </div>
    
    <div class="code-info">
        <h3>📋 Code Analysis</h3>
        <div class="feature">✅ Interactive input/output</div>
        <div class="feature">✅ Error handling for invalid expressions</div>
        <div class="feature">✅ Support for basic arithmetic operations</div>
        <div class="feature">✅ Power and modulo operations</div>
    </div>`;
    }
    // Todo App 감지
    else if (codeContent.includes('todo') || codeContent.includes('TodoApp')) {
      previewHTML += `
    <div class="preview-section">
        <h2>📝 Todo Application</h2>
        <div style="background: #000; padding: 20px; border-radius: 8px; font-family: monospace;">
            <div style="color: #0f0;">📝 Todo App</div>
            <div style="color: #58a6ff; margin: 10px 0;">1. Add todo</div>
            <div style="color: #58a6ff;">2. List todos</div>
            <div style="color: #58a6ff;">3. Complete todo</div>
            <div style="color: #58a6ff;">4. Delete todo</div>
            <div style="color: #58a6ff;">5. Quit</div>
            <div style="color: #fbbf24; margin-top: 10px;">Choose option (1-5): <span style="color: #fff;">1</span></div>
            <div style="color: #7dd3fc;">Enter new todo: Learn Python</div>
            <div style="color: #7dd3fc;">Added: Learn Python</div>
        </div>
    </div>
    
    <div class="code-info">
        <h3>📋 Code Analysis</h3>
        <div class="feature">✅ JSON data persistence</div>
        <div class="feature">✅ CRUD operations for todos</div>
        <div class="feature">✅ Interactive menu system</div>
        <div class="feature">✅ Error handling for user input</div>
    </div>`;
    }
    // Web Scraper 감지
    else if (codeContent.includes('requests') && codeContent.includes('BeautifulSoup')) {
      previewHTML += `
    <div class="preview-section">
        <h2>🌐 Web Scraper</h2>
        <div style="background: #000; padding: 20px; border-radius: 8px; font-family: monospace;">
            <div style="color: #0f0;">🕷️ Web Scraper Demo</div>
            <div style="color: #58a6ff; margin: 10px 0;">1. Scraping quotes...</div>
            <div style="color: #7dd3fc;">Found 10 quotes</div>
            <div style="color: #fbbf24;">1. "The way to get started is to quit talking and begin doing." - Walt Disney</div>
            <div style="color: #fbbf24;">2. "Life is what happens to you while you're busy making other plans." - John Lennon</div>
            <div style="color: #7dd3fc; margin-top: 10px;">Saved to quotes.json</div>
        </div>
    </div>
    
    <div class="code-info">
        <h3>📋 Code Analysis</h3>
        <div class="feature">✅ HTTP requests with error handling</div>
        <div class="feature">✅ HTML parsing with BeautifulSoup</div>
        <div class="feature">✅ JSON data export</div>
        <div class="feature">✅ User-Agent headers for compatibility</div>
    </div>`;
    }
    else {
      previewHTML += `
        <div style="background: #2d2d2d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0 0 15px 0;">🐍 Python Code</h2>
            <pre style="background: #0d1117; color: #58a6ff; padding: 10px; border-radius: 5px; overflow: auto; font-size: 12px; margin: 0;">${codeContent.substring(0, 500)}${codeContent.length > 500 ? '...' : ''}</pre>
        </div>`;
    }

    previewHTML += `
        <div style="color: #fbbf24; background: #451a03; padding: 10px; border-radius: 5px; margin: 10px 0;">
            ⚠️ This is a preview simulation. To run the actual code, save it as a .py file and execute with Python.
        </div>
    </div>`;

    return previewHTML;
  };

  // 코드 변경 시 미리보기 업데이트
  useEffect(() => {
    if (isOpen && code) {
      setIsLoading(true);
      const actualCode = extractCodeContent(code);
      const detectedLanguage = extractLanguageFromCode(code);
      
      // 디바운싱: 500ms 후에 업데이트
      const timer = setTimeout(() => {
        const htmlContent = generateHTMLPreview(actualCode, detectedLanguage);
        setPreviewContent(htmlContent);
        setIsLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [code, isOpen]);

  // iframe 콘텐츠는 이제 srcDoc으로 직접 설정됨

  if (!isOpen) return null;

  return (
    <div className="flex flex-col gap-4 w-full animate-in slide-in-from-right duration-500">
      {/* Live Preview Header */}
      <div className="bg-primary text-background p-4 retro-border">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <span className="text-accent">🔴</span>
              LIVE PREVIEW
              {isLoading && <span className="text-xs animate-pulse">UPDATING...</span>}
            </h2>
            <p className="text-sm opacity-80">{title} | Real-time execution</p>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onClose}
              className="px-3 py-1 bg-background text-primary retro-border pixel-press text-sm"
              title="Close live preview"
            >
              ✕ CLOSE
            </button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 retro-border bg-background relative preview-container">
        <div className="p-2 bg-muted border-b">
          <div className="flex items-center mb-1">
            <span className="text-primary font-bold mr-2">PREVIEW&gt;</span>
            <span className="text-secondary text-xs">
              {isLoading ? 'Updating...' : 'Live'}
            </span>
            {isLoading && (
              <div className="ml-2 flex items-center gap-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-1"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-2"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse loading-dot-3"></div>
              </div>
            )}
          </div>
        </div>

        {previewContent ? (
          <div
            className="w-full preview-iframe overflow-auto p-4"
            dangerouslySetInnerHTML={{ __html: previewContent }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-secondary">
            <div className="text-center">
              <div className="text-2xl mb-2">⚡</div>
              <div>Waiting for code...</div>
            </div>
          </div>
        )}
      </div>

      {/* Preview Status */}
      <div className="p-4 retro-border bg-background">
        <div className="flex justify-between items-center">
          <div className="text-xs text-secondary">
            {isLoading ? 'UPDATING PREVIEW...' : 'PREVIEW READY'} | Real-time sync enabled
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={onClose}
              className="bg-secondary text-background px-4 py-1 retro-border pixel-press text-sm"
              title="Close preview"
            >
              CLOSE
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
