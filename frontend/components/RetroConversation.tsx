import { useEffect, useRef } from 'react';

type Message = {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
};

interface RetroConversationProps {
  messages: Message[];
  isLoading: boolean;
}

export function RetroConversation({ messages, isLoading }: RetroConversationProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="flex-1 overflow-y-auto p-4 retro-border bg-background">
      {messages.map((message, index) => (
        <div key={index} className="mb-4">
          <div className="flex items-center mb-1">
            <span className="text-primary font-bold mr-2">
              {message.role === 'user' ? 'USER&gt;' : 'SYSTEM&gt;'}
            </span>
            <span className="text-secondary text-xs">{message.timestamp}</span>
          </div>
          <div className="whitespace-pre-wrap">{message.content}</div>
        </div>
      ))}
      {isLoading && (
        <div className="flex items-center">
          <span className="text-primary font-bold mr-2">SYSTEM&gt;</span>
          <span className="blink">_</span>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
}
