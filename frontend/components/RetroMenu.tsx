import { useState, useEffect } from 'react';
import { SettingsModal } from './SettingsModal';
import { Moon, Sun, Monitor, Trash2, X } from 'lucide-react';
import { ThemeMode, LanguageCode } from '../types';

interface RetroMenuProps {
  onClearChat: () => void;
  onLoadConversation: (conversationId: string) => void;
  onDeleteConversation: (conversationId: string) => void;
  onClearAllConversations: () => void;
  conversations: Array<{ id: string; title: string; timestamp: string }>;
  onThemeChange?: (theme: ThemeMode) => void;
  onLanguageChange?: (lang: LanguageCode) => void;
}

export function RetroMenu({
  onClearChat,
  onLoadConversation,
  onDeleteConversation,
  onClearAllConversations,
  conversations,
  onThemeChange = () => {},
  onLanguageChange = () => {}
}: RetroMenuProps) {
  const [showConversations, setShowConversations] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [deleteMode, setDeleteMode] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<ThemeMode>('system');
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');

  // Load saved settings from localStorage
  useEffect(() => {
    const savedTheme = (localStorage.getItem('theme') as ThemeMode) || 'system';
    const savedLanguage = (localStorage.getItem('language') as LanguageCode) || 'en';
    
    setCurrentTheme(savedTheme);
    setCurrentLanguage(savedLanguage);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (theme: ThemeMode) => {
    document.documentElement.classList.remove('light', 'dark');
    
    if (theme === 'system') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.add(isDark ? 'dark' : 'light');
    } else {
      document.documentElement.classList.add(theme);
    }
    
    localStorage.setItem('theme', theme);
    setCurrentTheme(theme);
    onThemeChange(theme);
  };

  const handleThemeChange = (theme: ThemeMode) => {
    applyTheme(theme);
    // Force re-render of the theme icon
    setCurrentTheme(theme);
  };

  const handleLanguageChange = (lang: LanguageCode) => {
    setCurrentLanguage(lang);
    localStorage.setItem('language', lang);
    onLanguageChange(lang);
  };
  return (
    <div className="w-full md:w-64 p-4 retro-border bg-background">
      <h2 className="text-xl text-primary mb-4">SYSTEM MENU</h2>
      
      <div className="space-y-2">
        <button
          type="button"
          className="w-full bg-muted text-primary p-2 retro-border pixel-press text-left"
          onClick={onClearChat}
        >
          [1] NEW CONVERSATION
        </button>

        <div className="relative">
          <button
            type="button"
            className="w-full bg-muted text-primary p-2 retro-border pixel-press text-left mb-1"
            onClick={() => setShowConversations(!showConversations)}
          >
            [2] LOAD CONVERSATION
          </button>
          
          {showConversations && (
            <div className="absolute left-0 right-0 bg-background border-2 border-primary z-10 max-h-60 overflow-y-auto">
              {conversations.length > 0 ? (
                <>
                  <div className="p-2 border-b border-primary">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs text-primary font-bold">SAVED CONVERSATIONS</span>
                      <div className="flex gap-1">
                        <button
                          type="button"
                          className={`px-2 py-1 text-xs border ${deleteMode ? 'bg-red-600 text-white border-red-600' : 'bg-muted text-primary border-primary'} hover:bg-opacity-80`}
                          onClick={() => setDeleteMode(!deleteMode)}
                          title={deleteMode ? "Exit delete mode" : "Delete conversations"}
                        >
                          {deleteMode ? <X className="w-3 h-3" /> : <Trash2 className="w-3 h-3" />}
                        </button>
                        <button
                          type="button"
                          className="px-2 py-1 text-xs bg-muted text-primary border border-primary hover:bg-accent"
                          onClick={() => {
                            setShowConversations(false);
                            setDeleteMode(false);
                          }}
                          title="Close"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                    {conversations.length > 0 && (
                      <button
                        type="button"
                        className="w-full px-2 py-1 text-xs bg-red-600 text-white border border-red-600 hover:bg-red-700"
                        onClick={() => {
                          if (confirm(`Delete all ${conversations.length} conversations? This cannot be undone.`)) {
                            onClearAllConversations();
                            setShowConversations(false);
                            setDeleteMode(false);
                          }
                        }}
                        title="Delete all conversations"
                      >
                        🗑️ DELETE ALL ({conversations.length})
                      </button>
                    )}
                  </div>
                  {conversations.map((conv) => (
                    <div
                      key={conv.id}
                      className={`p-2 text-sm flex items-center justify-between ${deleteMode ? 'hover:bg-red-100 dark:hover:bg-red-900' : 'hover:bg-accent cursor-pointer'}`}
                    >
                      <div
                        className="flex-1"
                        onClick={() => {
                          if (!deleteMode) {
                            onLoadConversation(conv.id);
                            setShowConversations(false);
                          }
                        }}
                      >
                        <div className="font-bold">{conv.title}</div>
                        <div className="text-xs text-muted-foreground">{conv.timestamp}</div>
                      </div>
                      {deleteMode && (
                        <button
                          type="button"
                          className="ml-2 p-1 bg-red-600 text-white hover:bg-red-700 rounded"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm(`Delete conversation "${conv.title}"?`)) {
                              onDeleteConversation(conv.id);
                            }
                          }}
                          title="Delete this conversation"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-2 text-sm text-muted-foreground">No saved conversations</div>
              )}
            </div>
          )}
        </div>
        
        <button
          type="button"
          className="w-full bg-muted text-primary p-2 retro-border text-left pixel-press"
          onClick={() => setShowSettings(true)}
        >
          [3] GENERAL SETTINGS
        </button>
      </div>
      
      <div className="mt-8 p-2 border-2 border-primary">
        <div className="text-xs text-secondary">SYSTEM STATUS</div>
        <div className="flex items-center justify-between mt-1">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-accent mr-2 pixel-spin"></div>
            <span className="text-primary">OPERATIONAL</span>
          </div>
          <button
            type="button"
            onClick={() => {
              const themes: ThemeMode[] = ['system', 'light', 'dark'];
              const currentIndex = themes.indexOf(currentTheme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              handleThemeChange(nextTheme);
            }}
            className="p-1"
            title={`Current theme: ${currentTheme}`}
            aria-label={`Toggle theme. Current: ${currentTheme}`}
          >
            {currentTheme === 'dark' ? (
              <Moon className="w-4 h-4" />
            ) : currentTheme === 'light' ? (
              <Sun className="w-4 h-4" />
            ) : (
              <Monitor className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        currentTheme={currentTheme}
        onThemeChange={handleThemeChange}
        currentLanguage={currentLanguage}
        onLanguageChange={handleLanguageChange}
      />
    </div>
  );
}
