import { useState, useEffect } from 'react';
import { SettingsModal } from './SettingsModal';
import { Moon, Sun, Monitor } from 'lucide-react';
import { ThemeMode, LanguageCode } from '../types';

interface RetroMenuProps {
  onClearChat: () => void;
  onLoadConversation: (conversationId: string) => void;
  conversations: Array<{ id: string; title: string; timestamp: string }>;
  onThemeChange?: (theme: ThemeMode) => void;
  onLanguageChange?: (lang: LanguageCode) => void;
}

export function RetroMenu({ 
  onClearChat, 
  onLoadConversation, 
  conversations,
  onThemeChange = () => {},
  onLanguageChange = () => {}
}: RetroMenuProps) {
  const [showConversations, setShowConversations] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<ThemeMode>('system');
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');

  // Load saved settings from localStorage
  useEffect(() => {
    const savedTheme = (localStorage.getItem('theme') as ThemeMode) || 'system';
    const savedLanguage = (localStorage.getItem('language') as LanguageCode) || 'en';
    
    setCurrentTheme(savedTheme);
    setCurrentLanguage(savedLanguage);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (theme: ThemeMode) => {
    document.documentElement.classList.remove('light', 'dark');
    
    if (theme === 'system') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.add(isDark ? 'dark' : 'light');
    } else {
      document.documentElement.classList.add(theme);
    }
    
    localStorage.setItem('theme', theme);
    setCurrentTheme(theme);
    onThemeChange(theme);
  };

  const handleThemeChange = (theme: ThemeMode) => {
    applyTheme(theme);
    // Force re-render of the theme icon
    setCurrentTheme(theme);
  };

  const handleLanguageChange = (lang: LanguageCode) => {
    setCurrentLanguage(lang);
    localStorage.setItem('language', lang);
    onLanguageChange(lang);
  };
  return (
    <div className="w-full md:w-64 p-4 retro-border bg-background">
      <h2 className="text-xl text-primary mb-4">SYSTEM MENU</h2>
      
      <div className="space-y-2">
        <button 
          className="w-full bg-muted text-primary p-2 retro-border pixel-press text-left"
          onClick={onClearChat}
        >
          [1] NEW CONVERSATION
        </button>
        
        <div className="relative">
          <button 
            className="w-full bg-muted text-primary p-2 retro-border pixel-press text-left mb-1"
            onClick={() => setShowConversations(!showConversations)}
          >
            [2] LOAD CONVERSATION
          </button>
          
          {showConversations && (
            <div className="absolute left-0 right-0 bg-background border-2 border-primary z-10 max-h-60 overflow-y-auto">
              {conversations.length > 0 ? (
                conversations.map((conv) => (
                  <div
                    key={conv.id}
                    className="p-2 hover:bg-accent cursor-pointer text-sm"
                    onClick={() => {
                      onLoadConversation(conv.id);
                      setShowConversations(false);
                    }}
                  >
                    <div className="font-bold">{conv.title}</div>
                    <div className="text-xs text-muted-foreground">{conv.timestamp}</div>
                  </div>
                ))
              ) : (
                <div className="p-2 text-sm text-muted-foreground">No saved conversations</div>
              )}
            </div>
          )}
        </div>
        
        <button 
          className="w-full bg-muted text-primary p-2 retro-border text-left pixel-press"
          onClick={() => setShowSettings(true)}
        >
          [3] GENERAL SETTINGS
        </button>
      </div>
      
      <div className="mt-8 p-2 border-2 border-primary">
        <div className="text-xs text-secondary">SYSTEM STATUS</div>
        <div className="flex items-center justify-between mt-1">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-accent mr-2 pixel-spin"></div>
            <span className="text-primary">OPERATIONAL</span>
          </div>
          <button 
            onClick={() => {
              const themes: ThemeMode[] = ['system', 'light', 'dark'];
              const currentIndex = themes.indexOf(currentTheme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              handleThemeChange(nextTheme);
            }} 
            className="p-1"
            title={`Current theme: ${currentTheme}`}
            aria-label={`Toggle theme. Current: ${currentTheme}`}
          >
            {currentTheme === 'dark' ? (
              <Moon className="w-4 h-4" />
            ) : currentTheme === 'light' ? (
              <Sun className="w-4 h-4" />
            ) : (
              <Monitor className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        currentTheme={currentTheme}
        onThemeChange={handleThemeChange}
        currentLanguage={currentLanguage}
        onLanguageChange={handleLanguageChange}
      />
    </div>
  );
}
