// Chat message types
export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  isCode?: boolean;
}

// Conversation types
export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  timestamp: string;
}

// API request/response types
export interface ChatRequest {
  query: string;
  language: string;
}

export interface ChatResponse {
  response: string;
  status: string;
  language: string;
  code_content?: string;
  instructions?: string;
  is_code?: boolean;
}

// Theme types
export type Theme = 'green' | 'blue' | 'amber' | 'red' | 'purple';

// Language types
export type Language = 'en' | 'ko';

// Component props types
export interface RetroHeaderProps {
  // No props needed currently
}

export interface RetroConversationProps {
  messages: Message[];
  isLoading: boolean;
}

export interface RetroInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}

export interface RetroMenuProps {
  onClearChat: () => void;
  onLoadConversation: (conversation: Conversation) => void;
  onDeleteConversation: (id: string) => void;
  onClearAllConversations: () => void;
  conversations: Conversation[];
  onThemeChange: (theme: Theme) => void;
  onLanguageChange: (language: Language) => void;
}

export interface LivePreviewProps {
  isOpen: boolean;
  onClose: () => void;
  code: string;
  language?: string;
  title?: string;
}

// Utility types
export interface TranslationFunction {
  (key: string): string;
}

export interface UseTranslationReturn {
  t: TranslationFunction;
  language: Language;
  setLanguage: (language: Language) => void;
}
