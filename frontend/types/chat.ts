// Chat message types
export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  isCode?: boolean;
}

// Conversation types
export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  timestamp: string;
}

// API request/response types
export interface ChatRequest {
  query: string;
  language: string;
}

export interface ChatResponse {
  response: string;
  status: string;
  language: string;
  code_content?: string;
  instructions?: string;
  is_code?: boolean;
}

// Language types
export type Language = 'en' | 'ko';
