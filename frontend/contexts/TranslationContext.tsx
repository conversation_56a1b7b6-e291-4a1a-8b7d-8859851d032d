import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { LanguageCode } from '../types';
import { translations } from '../utils/translations';
import { saveLanguage, loadLanguage } from '../utils/chat-utils';

interface TranslationContextType {
  language: LanguageCode;
  setLanguage: (language: LanguageCode) => void;
  t: typeof translations.en;
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

interface TranslationProviderProps {
  children: ReactNode;
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const [language, setLanguageState] = useState<LanguageCode>(() => {
    // Initialize from localStorage or default to 'en'
    const saved = loadLanguage();
    return (saved === 'en' || saved === 'ko') ? saved as LanguageCode : 'en';
  });

  const [, forceUpdate] = useState(0);

  // Get translations for current language
  const t = translations[language] || translations.en;

  const setLanguage = (newLanguage: LanguageCode) => {
    console.log('🌐 TranslationContext: Setting language to:', newLanguage);
    
    // Update state
    setLanguageState(newLanguage);
    
    // Save to localStorage
    saveLanguage(newLanguage);
    
    // Force re-render of all components using this context
    forceUpdate(prev => prev + 1);
    
    console.log('🌐 TranslationContext: Language change completed');
  };

  // Listen for language changes from localStorage (for cross-tab sync)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'language' && e.newValue) {
        const newLang = e.newValue as LanguageCode;
        if ((newLang === 'en' || newLang === 'ko') && newLang !== language) {
          console.log('🌐 TranslationContext: Language changed from storage:', newLang);
          setLanguageState(newLang);
          forceUpdate(prev => prev + 1);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [language]);

  const contextValue: TranslationContextType = {
    language,
    setLanguage,
    t,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
}
