import { useEffect, useState } from 'react';
import { LanguageCode } from '../types';
import { translations, Translations } from '../utils/translations';

export function useTranslation() {
  const [language, setLanguageState] = useState<LanguageCode>('en');
  const [t, setT] = useState<Translations>(translations.en);

  useEffect(() => {
    // Load language from localStorage
    const savedLanguage = localStorage.getItem('language') as LanguageCode;
    if (savedLanguage && translations[savedLanguage]) {
      setLanguageState(savedLanguage);
      setT(translations[savedLanguage]);
    }
  }, []);

  const setLanguage = (newLanguage: LanguageCode) => {
    setLanguageState(newLanguage);
    setT(translations[newLanguage]);
    localStorage.setItem('language', newLanguage);
  };

  const formatMessage = (message: string, params: Record<string, string | number> = {}) => {
    let formatted = message;
    Object.entries(params).forEach(([key, value]) => {
      formatted = formatted.replace(`{${key}}`, String(value));
    });
    return formatted;
  };

  return {
    language,
    setLanguage,
    t,
    formatMessage,
  };
}
