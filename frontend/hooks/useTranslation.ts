import { useEffect, useState } from 'react';
import { LanguageCode } from '../types';
import { translations, Translations } from '../utils/translations';

export function useTranslation() {
  const [language, setLanguageState] = useState<LanguageCode>('en');
  const [t, setT] = useState<Translations>(translations.en);
  const [, forceUpdate] = useState({});

  useEffect(() => {
    // Load language from localStorage
    const savedLanguage = localStorage.getItem('language') as LanguageCode;
    console.log('🌍 Initial load - saved language:', savedLanguage);
    console.log('🌍 Initial load - translation exists:', savedLanguage && translations[savedLanguage] ? 'Yes' : 'No');

    if (savedLanguage && translations[savedLanguage]) {
      console.log('🌍 Loading saved language:', savedLanguage);
      setLanguageState(savedLanguage);
      setT(translations[savedLanguage]);
    } else {
      console.log('🌍 Using default language: en');
    }
  }, []);

  const setLanguage = (newLanguage: LanguageCode) => {
    console.log('🌍 Setting language to:', newLanguage);
    console.log('🌍 Available translations:', Object.keys(translations));
    console.log('🌍 Translation for new language:', translations[newLanguage] ? 'Found' : 'Not found');

    setLanguageState(newLanguage);
    setT(translations[newLanguage]);
    localStorage.setItem('language', newLanguage);

    // 강제 리렌더링 트리거
    forceUpdate({});

    console.log('🌍 Language state updated to:', newLanguage);
    console.log('🌍 localStorage updated:', localStorage.getItem('language'));
    console.log('🌍 Force update triggered');
  };

  const formatMessage = (message: string, params: Record<string, string | number> = {}) => {
    let formatted = message;
    Object.entries(params).forEach(([key, value]) => {
      formatted = formatted.replace(`{${key}}`, String(value));
    });
    return formatted;
  };

  // 디버깅용 함수
  const debugLanguage = () => {
    console.log('🌍 Current language state:', language);
    console.log('🌍 Current translations:', t);
    console.log('🌍 localStorage language:', localStorage.getItem('language'));
  };

  return {
    language,
    setLanguage,
    t,
    formatMessage,
    debugLanguage, // 디버깅용
  };
}
