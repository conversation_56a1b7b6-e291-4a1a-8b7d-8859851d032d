interface ToastProps {
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
}

// 간단한 토스트 구현
export function toast(props: ToastProps) {
  const { title, description, variant = 'default' } = props;
  
  // 토스트 엘리먼트 생성
  const toastEl = document.createElement('div');
  toastEl.className = `fixed top-4 right-4 p-4 rounded retro-border z-50 ${
    variant === 'destructive' ? 'bg-destructive text-destructive-foreground' : 'bg-background text-foreground'
  }`;
  
  // 토스트 내용 추가
  toastEl.innerHTML = `
    <div class="font-bold">${title}</div>
    <div class="text-sm">${description}</div>
  `;
  
  // 문서에 추가
  document.body.appendChild(toastEl);
  
  // 3초 후 제거
  setTimeout(() => {
    toastEl.classList.add('opacity-0');
    toastEl.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
      document.body.removeChild(toastEl);
    }, 500);
  }, 3000);
}
