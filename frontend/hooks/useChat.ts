import { useState, useRef, useCallback } from 'react';
import { Message, Conversation, Language, Theme } from '../types/chat';
import { 
  getCurrentTime, 
  isCodeResponse, 
  extractCodeTitle,
  saveCurrentConversation,
  loadConversations,
  saveConversations,
  applyTheme,
  loadTheme,
  saveLanguage,
  loadLanguage,
  playNotificationSound,
  makeApiRequest,
  validateMessage,
  sanitizeMessage
} from '../utils/chat-utils';
import { useTranslation } from './useTranslation';

export const useChat = () => {
  // State
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isCodeViewerOpen, setIsCodeViewerOpen] = useState(false);
  const [currentCode, setCurrentCode] = useState('');
  const [codeTitle, setCodeTitle] = useState('');
  const [pendingInstructions, setPendingInstructions] = useState('');

  // Refs
  const audioRef = useRef<HTMLAudioElement>(null);

  // Hooks
  const { language, setLanguage } = useTranslation();

  // Initialize
  const initialize = useCallback(() => {
    const savedConversations = loadConversations();
    setConversations(savedConversations);

    const savedTheme = loadTheme();
    applyTheme(savedTheme);

    const savedLanguage = loadLanguage();
    setLanguage(savedLanguage as Language);
  }, [setLanguage]);

  // Audio
  const playAudio = useCallback(() => {
    playNotificationSound(audioRef);
  }, []);

  // Code streaming simulation
  const simulateCodeStreaming = useCallback((fullCode: string) => {
    setCurrentCode('');
    const lines = fullCode.split('\n');
    let currentLineIndex = 0;
    let currentCode = '';

    const streamInterval = setInterval(() => {
      if (currentLineIndex < lines.length) {
        currentCode += lines[currentLineIndex] + '\n';
        setCurrentCode(currentCode);
        currentLineIndex++;
      } else {
        clearInterval(streamInterval);
        // 코드 작성 완료 후 설명 추가
        if (pendingInstructions) {
          setTimeout(() => {
            const instructionMessage: Message = {
              role: 'assistant',
              content: pendingInstructions,
              timestamp: getCurrentTime(),
              isCode: false,
            };
            setMessages(prev => [...prev, instructionMessage]);
            playAudio();
          }, 1000); // 코드 완성 후 1초 뒤 설명 추가
        }
      }
    }, 100); // 100ms마다 한 줄씩 추가
  }, [pendingInstructions, playAudio]);

  // Code viewer handlers
  const handleCloseCodeViewer = useCallback(() => {
    setIsCodeViewerOpen(false);
    setPendingInstructions(''); // 설명 초기화
  }, []);

  // Message handling
  const handleSendMessage = useCallback(async (message: string) => {
    if (!validateMessage(message) || isLoading) return;

    const sanitizedMessage = sanitizeMessage(message);
    setIsLoading(true);

    const userMessage: Message = {
      role: 'user',
      content: sanitizedMessage,
      timestamp: getCurrentTime(),
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      const responseData = await makeApiRequest(sanitizedMessage, language);
      const responseText = responseData.response || "I'm sorry, I couldn't generate a response. Please try again.";

      const assistantMessage: Message = {
        role: 'assistant',
        content: responseText.trim() || "I'm sorry, I couldn't generate a response. Please try again.",
        timestamp: getCurrentTime(),
        isCode: responseData.is_code || isCodeResponse(responseText),
      };

      setMessages(prev => [...prev, assistantMessage]);

      // 코드 응답인 경우 라이브 미리보기 자동 열기
      if (responseData.is_code && responseData.code_content) {
        setIsCodeViewerOpen(true);
        setCodeTitle(extractCodeTitle(responseText));
        
        // 코드 스트리밍 효과
        simulateCodeStreaming(responseData.code_content);
        
        // 설명이 있으면 저장
        if (responseData.instructions) {
          setPendingInstructions(responseData.instructions);
        }
      } else if (isCodeResponse(responseText)) {
        // 기존 방식 (백워드 호환성)
        setCurrentCode(responseText);
        setCodeTitle(extractCodeTitle(responseText));
        setIsCodeViewerOpen(true);
      }

      playAudio();
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        role: 'assistant',
        content: 'Sorry, there was an error processing your request. Please try again.',
        timestamp: getCurrentTime(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, language, playAudio, simulateCodeStreaming]);

  // Conversation management
  const clearChat = useCallback(() => {
    if (messages.length > 0) {
      saveCurrentConversation(messages);
      const updatedConversations = loadConversations();
      setConversations(updatedConversations);
    }
    setMessages([]);
    setIsCodeViewerOpen(false);
    setCurrentCode('');
    setCodeTitle('');
    setPendingInstructions('');
  }, [messages]);

  const loadConversation = useCallback((conversation: Conversation) => {
    setMessages(conversation.messages);
    setIsCodeViewerOpen(false);
    setCurrentCode('');
    setCodeTitle('');
    setPendingInstructions('');
  }, []);

  const deleteConversation = useCallback((id: string) => {
    const updatedConversations = conversations.filter(conv => conv.id !== id);
    setConversations(updatedConversations);
    saveConversations(updatedConversations);
  }, [conversations]);

  const clearAllConversations = useCallback(() => {
    setConversations([]);
    saveConversations([]);
  }, []);

  // Theme and language handlers
  const handleThemeChange = useCallback((theme: Theme) => {
    applyTheme(theme);
  }, []);

  const handleLanguageChange = useCallback((newLanguage: Language) => {
    setLanguage(newLanguage);
    saveLanguage(newLanguage);
  }, [setLanguage]);

  return {
    // State
    messages,
    isLoading,
    conversations,
    isCodeViewerOpen,
    currentCode,
    codeTitle,
    language,
    
    // Refs
    audioRef,
    
    // Handlers
    handleSendMessage,
    clearChat,
    loadConversation,
    deleteConversation,
    clearAllConversations,
    handleThemeChange,
    handleLanguageChange,
    handleCloseCodeViewer,
    initialize,
  };
};
