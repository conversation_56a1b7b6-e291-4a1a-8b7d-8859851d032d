import { describe, it, expect } from 'vitest';
import { markdownToHtml } from './markdownToHtml';

// 간단한 마크다운 변환 테스트

describe('markdownToHtml', () => {
  it('헤더 변환', () => {
    expect(markdownToHtml('# 제목')).toContain('<h1>제목</h1>');
    expect(markdownToHtml('## 소제목')).toContain('<h2>소제목</h2>');
  });

  it('볼드/이탤릭 변환', () => {
    expect(markdownToHtml('**굵게**')).toContain('<strong>굵게</strong>');
    expect(markdownToHtml('*기울임*')).toContain('<em>기울임</em>');
  });

  it('링크 변환', () => {
    expect(markdownToHtml('[구글](https://google.com)')).toContain('<a href="https://google.com"');
  });

  it('리스트 변환', () => {
    expect(markdownToHtml('- 아이템1\n- 아이템2')).toContain('<ul>');
    expect(markdownToHtml('- 아이템1\n- 아이템2')).toContain('<li>아이템1</li>');
  });

  it('코드 변환', () => {
    expect(markdownToHtml('`코드`')).toContain('<code>코드</code>');
  });
});