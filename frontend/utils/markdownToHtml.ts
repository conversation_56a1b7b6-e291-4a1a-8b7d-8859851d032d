// 마크다운 문자열을 HTML 문자열로 변환하는 함수
// 각 변환 단계마다 로그를 추가하여 동작을 확인할 수 있도록 함
export function markdownToHtml(markdown: string): string {
  let html = markdown;
  console.log('[markdownToHtml] 입력:', markdown);

  // 헤더 변환 (h1~h6)
  html = html.replace(/^###### (.*)$/gm, '<h6>$1</h6>');
  html = html.replace(/^##### (.*)$/gm, '<h5>$1</h5>');
  html = html.replace(/^#### (.*)$/gm, '<h4>$1</h4>');
  html = html.replace(/^### (.*)$/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.*)$/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.*)$/gm, '<h1>$1</h1>');
  console.log('[markdownToHtml] 헤더 변환:', html);

  // 볼드 변환 (**텍스트**)
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  console.log('[markdownToHtml] 볼드 변환:', html);

  // 이탤릭 변환 (*텍스트*)
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  console.log('[markdownToHtml] 이탤릭 변환:', html);

  // 인라인 코드 변환 (`코드`)
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
  console.log('[markdownToHtml] 코드 변환:', html);

  // 링크 변환 [텍스트](url)
  html = html.replace(/\[([^\]]+)\]\(([^\)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
  console.log('[markdownToHtml] 링크 변환:', html);

  // 리스트 변환 (- 아이템)
  // 여러 줄의 - 아이템을 <ul><li>...</li></ul>로 변환
  html = html.replace(/(^|\n)(- .*(\n- .*)*)/g, (match) => {
    const items = match.trim().split(/\n/).map(line => line.replace(/^- /, '').trim());
    if (items.length > 1 || match.trim().startsWith('- ')) {
      return '\n<ul>' + items.map(item => `<li>${item}</li>`).join('') + '</ul>';
    }
    return match;
  });
  console.log('[markdownToHtml] 리스트 변환:', html);

  return html;
}
