import { Message, Conversation } from '../types/chat';

// Time utilities
export const getCurrentTime = (): string => {
  return new Date().toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// Code detection utilities
export const isCodeResponse = (content: string): boolean => {
  // 더 정확한 코드 감지 - 백틱으로 감싸진 코드 블록이나 명확한 코드 패턴만 감지
  return content.includes('```') ||
         (content.includes('def ') && content.includes(':')) ||
         (content.includes('function ') && content.includes('(')) ||
         (content.includes('class ') && content.includes(':')) ||
         (content.includes('import ') && content.includes('\n')) ||
         (content.includes('from ') && content.includes('import')) ||
         content.includes('#include <') ||
         (content.includes('package ') && content.includes(';'));
};

export const extractCodeTitle = (content: string): string => {
  if (content.includes('Snake Game') || content.includes('snake')) {
    return 'Snake Game';
  } else if (content.includes('Calculator') || content.includes('calculator')) {
    return 'Calculator';
  } else if (content.includes('Todo') || content.includes('todo')) {
    return 'Todo App';
  } else if (content.includes('Web Scraper') || content.includes('scraper')) {
    return 'Web Scraper';
  } else if (content.includes('```python')) {
    return 'Python Code';
  } else if (content.includes('```javascript')) {
    return 'JavaScript Code';
  } else if (content.includes('```html')) {
    return 'HTML Code';
  } else if (content.includes('```css')) {
    return 'CSS Code';
  } else {
    return 'Code';
  }
};



// Language utilities
export const saveLanguage = (language: string): void => {
  try {
    // Only allow English and Korean
    if (language === 'en' || language === 'ko') {
      localStorage.setItem('language', language);
    }
  } catch (error) {
    console.error('Failed to save language:', error);
  }
};

export const loadLanguage = (): string => {
  try {
    const saved = localStorage.getItem('language');
    // Only return saved language if it's English or Korean
    if (saved === 'en' || saved === 'ko') {
      return saved;
    }
    return 'en';
  } catch (error) {
    console.error('Failed to load language:', error);
    return 'en';
  }
};



// Session utilities
export const getSessionId = (): string => {
  let sessionId = localStorage.getItem('dos-chat-session-id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('dos-chat-session-id', sessionId);
  }
  return sessionId;
};

// API utilities
export const makeApiRequest = async (query: string, language: string) => {
  const sessionId = getSessionId();

  const response = await fetch('http://localhost:8000/ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: query,
      language: language,
      session_id: sessionId,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};


