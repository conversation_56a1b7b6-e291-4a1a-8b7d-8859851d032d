import { Message, Conversation } from '../types/chat';

// Time utilities
export const getCurrentTime = (): string => {
  return new Date().toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// Code detection utilities
export const isCodeResponse = (content: string): boolean => {
  // 더 정확한 코드 감지 - 백틱으로 감싸진 코드 블록이나 명확한 코드 패턴만 감지
  return content.includes('```') ||
         (content.includes('def ') && content.includes(':')) ||
         (content.includes('function ') && content.includes('(')) ||
         (content.includes('class ') && content.includes(':')) ||
         (content.includes('import ') && content.includes('\n')) ||
         (content.includes('from ') && content.includes('import')) ||
         content.includes('#include <') ||
         (content.includes('package ') && content.includes(';'));
};

export const extractCodeTitle = (content: string): string => {
  if (content.includes('Snake Game') || content.includes('snake')) {
    return 'Snake Game';
  } else if (content.includes('Calculator') || content.includes('calculator')) {
    return 'Calculator';
  } else if (content.includes('Todo') || content.includes('todo')) {
    return 'Todo App';
  } else if (content.includes('Web Scraper') || content.includes('scraper')) {
    return 'Web Scraper';
  } else if (content.includes('```python')) {
    return 'Python Code';
  } else if (content.includes('```javascript')) {
    return 'JavaScript Code';
  } else if (content.includes('```html')) {
    return 'HTML Code';
  } else if (content.includes('```css')) {
    return 'CSS Code';
  } else {
    return 'Code';
  }
};

// Local storage utilities
export const saveConversations = (conversations: Conversation[]): void => {
  try {
    localStorage.setItem('dos-chat-conversations', JSON.stringify(conversations));
  } catch (error) {
    console.error('Failed to save conversations:', error);
  }
};

export const loadConversations = (): Conversation[] => {
  try {
    const saved = localStorage.getItem('dos-chat-conversations');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Failed to load conversations:', error);
    return [];
  }
};

export const saveCurrentConversation = (messages: Message[]): void => {
  if (messages.length === 0) return;

  try {
    const conversations = loadConversations();
    const title = generateConversationTitle(messages[0].content);
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title,
      messages,
      timestamp: getCurrentTime(),
    };

    const updatedConversations = [newConversation, ...conversations.slice(0, 9)]; // Keep only 10 most recent
    saveConversations(updatedConversations);
  } catch (error) {
    console.error('Failed to save current conversation:', error);
  }
};

export const generateConversationTitle = (firstMessage: string): string => {
  const words = firstMessage.split(' ').slice(0, 4);
  return words.join(' ') + (firstMessage.split(' ').length > 4 ? '...' : '');
};

// Theme utilities
export const applyTheme = (theme: string): void => {
  try {
    localStorage.setItem('dos-chat-theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
  } catch (error) {
    console.error('Failed to apply theme:', error);
  }
};

export const loadTheme = (): string => {
  try {
    return localStorage.getItem('dos-chat-theme') || 'green';
  } catch (error) {
    console.error('Failed to load theme:', error);
    return 'green';
  }
};

// Language utilities
export const saveLanguage = (language: string): void => {
  try {
    // Only allow English and Korean
    if (language === 'en' || language === 'ko') {
      localStorage.setItem('language', language);
    }
  } catch (error) {
    console.error('Failed to save language:', error);
  }
};

export const loadLanguage = (): string => {
  try {
    const saved = localStorage.getItem('language');
    // Only return saved language if it's English or Korean
    if (saved === 'en' || saved === 'ko') {
      return saved;
    }
    return 'en';
  } catch (error) {
    console.error('Failed to load language:', error);
    return 'en';
  }
};

// Audio utilities
export const playNotificationSound = (audioRef: React.RefObject<HTMLAudioElement>): void => {
  try {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(error => {
        console.log('Audio play failed (this is normal on first load):', error);
      });
    }
  } catch (error) {
    console.error('Failed to play notification sound:', error);
  }
};

// Session utilities
export const getSessionId = (): string => {
  let sessionId = localStorage.getItem('dos-chat-session-id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('dos-chat-session-id', sessionId);
  }
  return sessionId;
};

// API utilities
export const makeApiRequest = async (query: string, language: string) => {
  const sessionId = getSessionId();

  const response = await fetch('http://localhost:8000/ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: query,
      language: language,
      session_id: sessionId,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Validation utilities
export const validateMessage = (message: string): boolean => {
  return message.trim().length > 0 && message.trim().length <= 1000;
};

export const sanitizeMessage = (message: string): string => {
  return message.trim().replace(/\s+/g, ' ');
};
