import { LanguageCode } from '../types';

export interface Translations {
  // Header
  systemReady: string;
  
  // Menu
  systemMenu: string;
  newConversation: string;
  loadConversation: string;
  generalSettings: string;
  operational: string;
  
  // Conversations
  savedConversations: string;
  deleteAll: string;
  noSavedConversations: string;
  deleteConversation: string;
  exitDeleteMode: string;
  close: string;
  deleteAllConfirm: string;
  deleteConversationConfirm: string;
  
  // Settings
  settings: string;
  theme: string;
  language: string;
  light: string;
  dark: string;
  system: string;
  
  // Languages
  english: string;
  korean: string;
  
  // Input
  typeQuestion: string;
  pressEnter: string;
  ready: string;
  processing: string;
  send: string;
  sending: string;
  
  // Messages
  welcomeMessage: string;
  conversationCleared: string;
  
  // Common
  cancel: string;
  confirm: string;
  delete: string;
  save: string;
}

export const translations: Record<LanguageCode, Translations> = {
  en: {
    // Header
    systemReady: 'SYSTEM READY - AWAITING INPUT',
    
    // Menu
    systemMenu: 'SYSTEM MENU',
    newConversation: '[1] NEW CONVERSATION',
    loadConversation: '[2] LOAD CONVERSATION',
    generalSettings: '[3] GENERAL SETTINGS',
    operational: 'OPERATIONAL',
    
    // Conversations
    savedConversations: 'SAVED CONVERSATIONS',
    deleteAll: 'DELETE ALL',
    noSavedConversations: 'No saved conversations',
    deleteConversation: 'Delete this conversation',
    exitDeleteMode: 'Exit delete mode',
    close: 'Close',
    deleteAllConfirm: 'Delete all {count} conversations? This cannot be undone.',
    deleteConversationConfirm: 'Delete conversation "{title}"?',
    
    // Settings
    settings: 'Settings',
    theme: 'Theme',
    language: 'Language',
    light: 'Light',
    dark: 'Dark',
    system: 'System',
    
    // Languages
    english: 'English',
    korean: '한국어',
    
    // Input
    typeQuestion: 'Type your question here... (Press Enter to send, Shift+Enter for new line)',
    pressEnter: 'Press Enter to send',
    ready: 'READY',
    processing: 'PROCESSING...',
    send: 'SEND',
    sending: 'SENDING...',
    
    // Messages
    welcomeMessage: 'WELCOME TO DOS-Chat\\n\\nTYPE YOUR QUESTION AND PRESS [ENTER] TO BEGIN',
    conversationCleared: 'CONVERSATION CLEARED. HOW MAY I ASSIST YOU?',
    
    // Common
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    save: 'Save',
  },
  
  ko: {
    // Header
    systemReady: '시스템 준비 완료 - 입력 대기 중',
    
    // Menu
    systemMenu: '시스템 메뉴',
    newConversation: '[1] 새 대화',
    loadConversation: '[2] 대화 불러오기',
    generalSettings: '[3] 일반 설정',
    operational: '작동 중',
    
    // Conversations
    savedConversations: '저장된 대화',
    deleteAll: '모두 삭제',
    noSavedConversations: '저장된 대화가 없습니다',
    deleteConversation: '이 대화 삭제',
    exitDeleteMode: '삭제 모드 종료',
    close: '닫기',
    deleteAllConfirm: '모든 {count}개의 대화를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.',
    deleteConversationConfirm: '"{title}" 대화를 삭제하시겠습니까?',
    
    // Settings
    settings: '설정',
    theme: '테마',
    language: '언어',
    light: '밝게',
    dark: '어둡게',
    system: '시스템',
    
    // Languages
    english: 'English',
    korean: '한국어',
    
    // Input
    typeQuestion: '질문을 입력하세요... (Enter로 전송, Shift+Enter로 줄바꿈)',
    pressEnter: 'Enter로 전송',
    ready: '준비',
    processing: '처리 중...',
    send: '전송',
    sending: '전송 중...',
    
    // Messages
    welcomeMessage: 'DOS-Chat에 오신 것을 환영합니다\\n\\n질문을 입력하고 [ENTER]를 눌러 시작하세요',
    conversationCleared: '대화가 지워졌습니다. 어떻게 도와드릴까요?',
    
    // Common
    cancel: '취소',
    confirm: '확인',
    delete: '삭제',
    save: '저장',

  },
};
