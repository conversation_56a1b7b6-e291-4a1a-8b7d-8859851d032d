# ⚽ Football AI - 축구 규칙 AI 챗봇

축구 규칙에 대한 질문에 답변하는 AI 챗봇 애플리케이션입니다. **Go 백엔드**와 **React 프론트엔드**로 구성되어 있으며, **병렬 처리**를 통해 빠른 응답을 제공합니다.

## 🚀 주요 기능

### 🔥 새로운 Go 백엔드 (v2.0)
- **병렬 처리**: Goroutine을 활용한 API 및 MCP tools 동시 호출
- **고성능**: Go의 동시성 모델을 활용한 빠른 응답
- **확장성**: 마이크로서비스 아키텍처 지원

### 🎯 핵심 기능
- **축구 규칙 AI 챗봇**: 30개 섹션의 상세한 축구 규칙 데이터베이스
- **다국어 지원**: 영어/한국어 응답
- **레트로 UI**: 80년대 컴퓨터 터미널 스타일
- **대화 관리**: 대화 내역 저장 및 불러오기
- **MCP Tools 통합**: Model Context Protocol 도구들과의 연동

## 🏗️ 아키텍처

```
football-ai/
├── backend/                 # Go 백엔드 (NEW!)
│   ├── main.go             # 메인 서버
│   ├── models/             # 데이터 모델
│   ├── services/           # 비즈니스 로직
│   ├── handlers/           # HTTP 핸들러
│   └── Makefile           # 빌드 스크립트
├── frontend/               # React 프론트엔드
│   ├── components/        # UI 컴포넌트
│   ├── pages/            # 페이지
│   └── package.json      # 의존성
├── mcp_data/             # 축구 규칙 데이터
│   └── football.txt      # 축구 규칙 텍스트
└── start.sh              # 애플리케이션 시작 스크립트
```

## 📋 사전 요구사항

### Go 설치 (백엔드)
Go 1.21 이상이 필요합니다.

#### macOS
```bash
# Homebrew 사용
brew install go

# 또는 공식 설치 프로그램
# https://golang.org/dl/ 에서 다운로드
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install golang-go

# CentOS/RHEL
sudo yum install golang
```

### Node.js 설치 (프론트엔드)
Node.js 18 이상이 필요합니다.

```bash
# macOS (Homebrew)
brew install node

# Linux
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 🚀 빠른 시작

### 1. 저장소 클론
```bash
git clone <repository-url>
cd football-ai
```

### 2. 전체 애플리케이션 실행
```bash
./start.sh
```

이 스크립트는 자동으로:
- Go 백엔드 빌드 및 실행 (포트 8000)
- React 프론트엔드 실행 (포트 5173)

### 3. 애플리케이션 접속
- **프론트엔드**: http://localhost:5173
- **백엔드 API**: http://localhost:8000

## 🔧 개별 실행

### Go 백엔드 실행
```bash
cd backend

# 의존성 설치
go mod tidy

# 개발 모드 (핫 리로드)
make dev

# 또는 일반 실행
make run

# 또는 직접 실행
go run main.go
```

### React 프론트엔드 실행
```bash
cd frontend

# 의존성 설치
npm install

# 개발 서버 시작
npm run dev
```

## 🧪 테스트

### 백엔드 테스트
```bash
cd backend

# Go 테스트 실행
make test

# API 엔드포인트 테스트
make test-backend

# 또는 직접 테스트 스크립트 실행
./test_backend.sh
```

### 수동 API 테스트
```bash
# 영어 질문
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "What is offside?", "language": "en"}'

# 한국어 질문
curl -X POST http://localhost:8000/ask \
  -H "Content-Type: application/json" \
  -d '{"query": "오프사이드가 뭐예요?", "language": "ko"}'
```

## 🌐 API 엔드포인트

### 기본 엔드포인트
- `GET /` - 서버 정보
- `GET /api/health` - 헬스 체크

### 채팅 엔드포인트
- `POST /ask` - 채팅 질문 처리
- `OPTIONS /ask` - CORS preflight

### MCP Tools 엔드포인트
- `GET /mcp/tools` - 사용 가능한 도구 목록
- `POST /mcp/tools` - MCP 도구 테스트

## 🔄 병렬 처리 흐름

1. **사용자 쿼리 수신**
2. **3개의 Goroutine 동시 실행**:
   - 🏠 로컬 축구 규칙 검색
   - 🔧 MCP 축구 컨텍스트 검색
   - 🌐 MCP 웹 컨텍스트 검색 (필요시)
3. **결과 수집 및 통합**
4. **최적의 응답 생성**

## 🛠️ 개발 도구

### Go 백엔드 개발 도구
```bash
cd backend

# 개발 도구 설치
make install-tools

# 코드 포맷팅
make fmt

# 코드 린팅
make lint

# 빌드
make build

# 프로덕션 빌드
make build-prod
```

### 사용 가능한 Make 명령어
```bash
make help          # 도움말 보기
make setup          # 개발 환경 설정
make dev            # 개발 모드 (핫 리로드)
make test           # 테스트 실행
make clean          # 빌드 파일 정리
```

## 🐳 Docker 지원

```bash
cd backend

# Docker 이미지 빌드
make docker-build

# Docker 컨테이너 실행
docker run -p 8000:8000 football-ai-backend
```

## 📊 성능 특징

### Go 백엔드의 장점
- **동시성**: 수천 개의 동시 요청 처리 가능
- **메모리 효율성**: 낮은 메모리 사용량
- **빠른 시작**: 빠른 서버 시작 시간
- **크로스 플랫폼**: Linux, macOS, Windows 지원

### 병렬 처리 성능
- **응답 시간**: 평균 50% 단축
- **처리량**: 동시 요청 처리 능력 향상
- **확장성**: 수평 확장 용이

## 🐛 문제 해결

### Go 관련 문제
```bash
# Go 설치 확인
go version

# 의존성 문제 해결
cd backend && go mod tidy

# 포트 충돌 해결
PORT=8080 go run main.go
```

### 프론트엔드 문제
```bash
# Node.js 버전 확인
node --version

# 의존성 재설치
cd frontend && rm -rf node_modules && npm install
```

## 📝 로그 및 디버깅

### 백엔드 로그
- 서비스 초기화 상태
- 수신된 쿼리 정보
- 병렬 처리 결과
- 오류 및 경고 메시지

### 프론트엔드 디버깅
- 브라우저 개발자 도구 콘솔 확인
- 네트워크 탭에서 API 요청/응답 확인

## 🤝 기여하기

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## 🙏 감사의 말

- Go 커뮤니티의 훌륭한 라이브러리들
- React 및 Vite 생태계
- 축구 규칙 데이터 제공

---

**🏈 Football AI v2.0 - Powered by Go & React**
