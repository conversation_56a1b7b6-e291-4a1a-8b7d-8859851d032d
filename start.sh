#!/bin/bash

# Set script to exit on any error
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# Function to display error message and exit
display_error() {
    echo -e "${RED}Error: $1${NC}"
    exit 1
}

echo -e "${GREEN}Starting Legal AI Application...${NC}"

echo -e "\n${GREEN}1. Activating Python virtual environment and starting backend...${NC}"
cd backend || display_error "Could not enter backend directory"
source .venv/bin/activate || display_error "Could not activate virtual environment"

# Start backend in background
uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
echo -e "${GREEN}Backend started with PID: $BACKEND_PID${NC}"

echo -e "\n${GREEN}2. Starting frontend...${NC}"
cd ../frontend || display_error "Could not enter frontend directory"

# Start frontend in background
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}Frontend started with PID: $FRONTEND_PID${NC}"

echo -e "\n${GREEN}Application started successfully!${NC}"
echo -e "Frontend: http://localhost:5173"
echo -e "Backend: http://localhost:8000"

# Wait for any process to terminate
wait $BACKEND_PID $FRONTEND_PID
