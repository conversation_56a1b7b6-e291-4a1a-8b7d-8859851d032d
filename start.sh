#!/bin/bash

# Set script to exit on any error
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# Function to display error message and exit
display_error() {
    echo -e "${RED}Error: $1${NC}"
    exit 1
}

echo -e "${GRE<PERSON>}Starting Football AI Application...${NC}"

echo -e "\n${GREEN}1. Building and starting Go backend...${NC}"
cd backend || display_error "Could not enter backend directory"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    display_error "Go is not installed. Please install Go 1.21 or later."
fi

# Initialize Go modules if not already done
if [ ! -f "go.sum" ]; then
    echo -e "${GREEN}Initializing Go modules...${NC}"
    go mod tidy || display_error "Failed to initialize Go modules"
fi

# Build the Go application
echo -e "${GREEN}Building Go application...${NC}"
go build -o football-ai-backend . || display_error "Failed to build Go application"

# Start backend in background
./football-ai-backend &
BACKEND_PID=$!
echo -e "${GREEN}Go backend started with PID: $BACKEND_PID${NC}"

echo -e "\n${GREEN}2. Starting frontend...${NC}"
cd ../frontend || display_error "Could not enter frontend directory"

# Start frontend in background
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}Frontend started with PID: $FRONTEND_PID${NC}"

echo -e "\n${GREEN}Application started successfully!${NC}"
echo -e "Frontend: http://localhost:5173"
echo -e "Backend: http://localhost:8000"

# Wait for any process to terminate
wait $BACKEND_PID $FRONTEND_PID
